[DEFAULT]
columns = 涨幅:pct_chg,代码:ts_code,名称:name,申万行业:申万行业,可比行业:可比行业,股价:close,50天:last50_up_count,连板:max_streak,天:limit_streak_days_ago,历史:history_max_streak,市值Z:市值Z,流通股:float_share,涨停:limit_times,上市日期:list_date,城市:city,省份:province,概念:concept
column_widths = 涨幅:112,代码:117,名称:125,申万行业:486,可比行业:206,股价:92,50天:93,连板:80,天:50,历史:80,市值Z:110,流通股:110,涨停:70,上市日期:138,城市:95,省份:95,概念:1028
numeric_columns = 股价,市值Z,流通股,涨幅,50天,连板,历史
left_aligned_columns = 名称,申万行业,可比行业,城市,省份,概念
right_aligned_columns = 涨幅,代码,股价,市值Z,流通股,50天,连板,天,历史,上市日期
n板天数 = 36
header_height = 80

[QueryPanel]
input_width = 16
input_height = 16
combobox_width = 16
combobox_height = 16
font_size = 10
row_spacing = 8
col_spacing = 18

[API]
token = 284b804f2f919ea85cb7e6dfe617ff81f123c80b4cd3c4b13b35d736

[Settings]
batch_size = 1000
threads = 8
go_bj_default_checked = 0

[Table]
row_height = 80
font_size = 10

[Window]
width = 3030
height = 1700
x_offset = 10
y_offset = 10
popup_width = 1200
popup_height = 600

[Path]
ths_path = D:\股票\羽扇纶巾24082920
db_path = D:/python_study/常用python程序/新数据导入/file/stock.db

[Tencent]
tencent_batch_size = 60
tencent_interval = 0.4
tencent_interval_jitter = 0.05
tencent_preheat_interval = 2.0
tencent_open_time = 09:30
tencent_preheat_minutes = 5
tencent_error_pause = 300

[Sina]
sina_batch_size = 40
sina_interval = 0.4
sina_interval_jitter = 0.05
sina_preheat_interval = 2.0
sina_open_time = 09:30
sina_preheat_minutes = 5
sina_error_pause = 300

[Crawl]
url = http://www.iwencai.com/unifiedwap/result?w=主营业务，申万行业分类，同花顺行业，自由流通Z，所属同花顺概念，上市日期，城市，省份，上市板块&querytype=stock
mode = 1
amount = 5
save_path = 问财数据.xlsx

[Browser]
user_data_dir = 
headless = false
browser_type = auto
retry_count = 3
timeout = 60

[Package]
output_dir = D:/python_study/常用python程序/新数据导入/file
icon_path = D:/Program Files/Flatastic_11_Icons/Click-cloud-icon.ico
python_path = C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\python.exe
main_script = D:/python_study/常用python程序/新数据导入/file/prompt_generator.py
onefile = True
console = False
use_spec = False
spec_file = start.spec
debug = false
hidden_imports = pandas,numpy,tkinter,sqlite3,tushare
data_files = 
extra_args = 

