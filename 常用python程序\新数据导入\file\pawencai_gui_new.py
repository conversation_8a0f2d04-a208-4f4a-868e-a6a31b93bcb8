import sys
import os
import time
import threading
import json
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QTabWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QComboBox, QPushButton, QCheckBox, QFileDialog,
                             QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QProgressBar, QTextEdit, QMessageBox, QSplitter, QFrame, QGroupBox,
                             QRadioButton, QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, QDateTimeEdit)
from PyQt5.QtCore import Qt, QTimer, QDateTime, QDate, QTime, QSize, QThread, pyqtSignal, QObject
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap, QPalette, QBrush
from package_tool import PackageTool

# 导入Excel处理模块
from excel_processor import ExcelProcessorWidget

# 导入数据库导入模块
from db_importer import DatabaseImporterWidget

# 导入股票更新模块
from stock_updater import StockUpdaterWidget

# 导入股票数据验证模块
from stock_validator import StockValidatorWidget

# 导入数据库调试模块
from db_debugger import DbDebuggerWidget

# 导入同花顺概念更新模块
from ths_concept_updater_qt_fixed import ThsConceptUpdater

# 导入开盘啦数据管理模块
from kpl_data_qt import KplDataQtWidget

# 导入可比公司模块
from comparable_companies import ComparableCompaniesWidget

# 申万行业导出功能已集成到Excel处理标签页中

class WorkerSignals(QObject):
    """
    定义工作线程的信号
    """
    # 使用一个简单的字符串信号，避免QTextCursor问题
    log_message = pyqtSignal(str)
    update_progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)

    # 添加一个用于安全更新日志的信号
    safe_append_text = pyqtSignal(str, object)  # 文本内容, QTextEdit对象


class PackagingThread(QThread):
    """
    打包线程类，用于在后台执行打包过程
    """
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.signals = WorkerSignals()
    
    def run(self):
        try:
            # 构建命令
            if self.config["use_spec"]:
                # 使用spec文件打包
                cmd = [self.config["python_path"], "-m", "PyInstaller"]
                
                # 添加输出目录
                if self.config["output_dir"]:
                    cmd.extend(["--distpath", self.config["output_dir"]])
                
                # 添加spec文件
                cmd.append(self.config["spec_file"])
                
                # 添加-y参数以确认覆盖
                cmd.append("-y")
                
                self.signals.log_message.emit("使用spec文件打包...")
            else:
                # 直接打包
                cmd = [self.config["python_path"], "-m", "PyInstaller"]
                
                # 添加选项
                if self.config["onefile"]:
                    cmd.append("--onefile")
                else:
                    cmd.append("--onedir")
                    
                if not self.config["console"]:
                    cmd.append("--noconsole")
                    
                if self.config["icon_path"] and os.path.exists(self.config["icon_path"]):
                    cmd.extend(["--icon", self.config["icon_path"]])
                
                # 添加输出目录
                if self.config["output_dir"]:
                    cmd.extend(["--distpath", self.config["output_dir"]])
                
                # 添加隐藏导入
                if self.config["hidden_imports"]:
                    for hidden_import in self.config["hidden_imports"].split(','):
                        if hidden_import.strip():
                            cmd.extend(["--hidden-import", hidden_import.strip()])
                
                # 添加数据文件
                if self.config["data_files"]:
                    for data_file in self.config["data_files"].split(','):
                        if data_file.strip():
                            cmd.extend(["--add-data", data_file.strip()])
                
                # 添加额外参数
                if self.config["extra_args"]:
                    cmd.extend(self.config["extra_args"].split())
                
                # 添加-y参数以确认覆盖
                cmd.append("-y")
                
                # 添加主脚本
                cmd.append(self.config["main_script"])
                
                self.signals.log_message.emit("直接使用PyInstaller命令打包...")
            
            # 执行命令
            self.signals.log_message.emit(f"执行命令: {' '.join(cmd)}")
            self.signals.update_progress.emit(10)
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding="utf-8",
                errors="replace"
            )
            
            # 实时读取输出
            for line in process.stdout:
                self.signals.log_message.emit(line.strip())
                # 根据输出更新进度条
                if "Building PKG" in line:
                    self.signals.update_progress.emit(50)
                elif "Building EXE" in line:
                    self.signals.update_progress.emit(70)
                elif "Building COLLECT" in line:
                    self.signals.update_progress.emit(90)
            
            # 等待进程结束
            return_code = process.wait()
            self.signals.update_progress.emit(100)
            
            if return_code == 0:
                self.signals.log_message.emit("打包成功!")
                self.signals.log_message.emit(f"输出目录: {self.config['output_dir']}")
                self.signals.finished.emit()
            else:
                self.signals.error.emit(f"打包失败，返回码: {return_code}")
        except Exception as e:
            self.signals.error.emit(f"打包过程中发生错误: {str(e)}")



class PawencaiGUI(QMainWindow):
    # 添加日志信号
    log_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.initUI()
        self.process = None
        
        # 连接日志信号到处理函数
        self.log_signal.connect(self.update_log_text)

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle('数据处理工具')
        self.setGeometry(100, 100, 1800, 850)  # 增大窗口尺寸以适应更宽的标签

        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: #4a86e8;
            }
            QPushButton {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                font-size: 24px;  /* 大幅增大字体大小 */
                padding: 15px 25px;
                border-radius: 10px;
                min-height: 60px;
                min-width: 200px;  /* 设置最小宽度 */
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }
            QLabel {
                font-size: 10pt;
            }
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4a86e8;
                width: 10px;
                margin: 0.5px;
            }
        """)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setDocumentMode(True)
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        self.tab_widget.setUsesScrollButtons(True)  # 启用滚动按钮，防止标签过多时挤在一起

        # 创建Excel处理标签页
        self.excel_processor_widget = ExcelProcessorWidget()
        self.tab_widget.addTab(self.excel_processor_widget, "Excel处理")

        # 创建数据库导入标签页
        self.db_importer_widget = DatabaseImporterWidget()
        self.tab_widget.addTab(self.db_importer_widget, "数据库导入")

        # 创建股票更新标签页
        self.stock_updater_widget = StockUpdaterWidget()
        self.tab_widget.addTab(self.stock_updater_widget, "股票数据更新")

        # 创建股票数据验证标签页
        self.stock_validator_widget = StockValidatorWidget()
        self.tab_widget.addTab(self.stock_validator_widget, "股票数据验证")

        # 创建数据库调试标签页
        self.db_debugger_widget = DbDebuggerWidget()
        self.tab_widget.addTab(self.db_debugger_widget, "数据库调试")

        # 创建同花顺概念更新标签页
        self.ths_concept_updater = ThsConceptUpdater()
        self.tab_widget.addTab(self.ths_concept_updater, "同花顺")
        
        # 创建开盘啦数据管理标签页
        self.kpl_data_widget = KplDataQtWidget()
        self.tab_widget.addTab(self.kpl_data_widget, "开盘啦")
        
        # 创建可比公司标签页
        self.comparable_companies_widget = ComparableCompaniesWidget()
        self.tab_widget.addTab(self.comparable_companies_widget, "可比公司")

        # 申万行业导出功能已集成到Excel处理标签页中

        # 创建打包标签页
        self.build_exe_tab = QWidget()
        self.setup_build_exe_tab()
        self.tab_widget.addTab(self.build_exe_tab, "打包为EXE")

        # 创建状态栏
        self.statusBar().showMessage("版本: 1.0.0")

        # 设置标签页样式
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
                top: -1px;
            }
            QTabBar::tab {
                min-width: 200px;
                min-height: 35px;
                font-size: 12pt;
                margin-right: 6px;
                padding: 8px 12px;
                border: 1px solid #ccc;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                border: 1px solid #3a76d8;
                border-bottom: none;
            }
            QTabBar::tab:!selected {
                background-color: #e8e8e8;
                margin-top: 2px;
            }
            QTabBar::tab:!selected:hover {
                background-color: #d0d0d0;
            }
        """)

        # 设置中央部件
        self.setCentralWidget(self.tab_widget)

    def setup_build_exe_tab(self):
        """设置打包标签页"""
        # 创建打包标签页的布局
        main_layout = QVBoxLayout()

        # 创建标题标签
        title_label = QLabel('打包应用为EXE文件')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont('Arial', 16, QFont.Bold))
        title_label.setStyleSheet("color: #4a86e8; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建设置区域
        settings_group = QGroupBox('打包设置')
        settings_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 12pt; }")
        settings_layout = QVBoxLayout()
        
        # 加载配置
        self.package_config = self.load_package_config()
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        output_label = QLabel("输出目录:")
        output_label.setMinimumWidth(80)
        self.output_dir_edit = QLineEdit(self.package_config.get("output_dir", ""))
        output_button = QPushButton("浏览...")
        output_button.setFixedWidth(100)
        output_button.clicked.connect(self.select_output_dir)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(output_button)
        settings_layout.addLayout(output_layout)
        
        # 图标选择
        icon_layout = QHBoxLayout()
        icon_label = QLabel("图标文件:")
        icon_label.setMinimumWidth(80)
        self.icon_path_edit = QLineEdit(self.package_config.get("icon_path", ""))
        icon_button = QPushButton("浏览...")
        icon_button.setFixedWidth(100)
        icon_button.clicked.connect(self.select_icon)
        icon_layout.addWidget(icon_label)
        icon_layout.addWidget(self.icon_path_edit)
        icon_layout.addWidget(icon_button)
        settings_layout.addLayout(icon_layout)
        
        # Python解释器路径
        python_layout = QHBoxLayout()
        python_label = QLabel("Python路径:")
        python_label.setMinimumWidth(80)
        self.python_path_edit = QLineEdit(self.package_config.get("python_path", sys.executable))
        python_button = QPushButton("浏览...")
        python_button.setFixedWidth(100)
        python_button.clicked.connect(self.select_python_path)
        python_layout.addWidget(python_label)
        python_layout.addWidget(self.python_path_edit)
        python_layout.addWidget(python_button)
        settings_layout.addLayout(python_layout)
        
        # 主脚本文件
        script_layout = QHBoxLayout()
        script_label = QLabel("主脚本文件:")
        script_label.setMinimumWidth(80)
        self.main_script_edit = QLineEdit(self.package_config.get("main_script", "pawencai_gui_new.py"))
        script_button = QPushButton("浏览...")
        script_button.setFixedWidth(100)
        script_button.clicked.connect(self.select_main_script)
        script_layout.addWidget(script_label)
        script_layout.addWidget(self.main_script_edit)
        script_layout.addWidget(script_button)
        settings_layout.addLayout(script_layout)
        
        # 打包选项
        options_group = QGroupBox('打包选项')
        options_layout = QVBoxLayout()
        
        # 单文件模式
        self.onefile_check = QCheckBox("打包为单个文件")
        self.onefile_check.setChecked(self.package_config.get("onefile", False))
        options_layout.addWidget(self.onefile_check)
        
        # 显示控制台
        self.console_check = QCheckBox("显示控制台窗口")
        self.console_check.setChecked(self.package_config.get("console", False))
        options_layout.addWidget(self.console_check)
        
        # 使用spec文件
        self.use_spec_check = QCheckBox("使用spec文件")
        self.use_spec_check.setChecked(self.package_config.get("use_spec", True))
        self.use_spec_check.stateChanged.connect(self.toggle_spec_file)
        options_layout.addWidget(self.use_spec_check)
        
        # spec文件选择
        spec_layout = QHBoxLayout()
        spec_label = QLabel("Spec文件:")
        spec_label.setMinimumWidth(80)
        self.spec_file_edit = QLineEdit(self.package_config.get("spec_file", "start.spec"))
        spec_button = QPushButton("浏览...")
        spec_button.setFixedWidth(100)
        spec_button.clicked.connect(self.select_spec_file)
        spec_layout.addWidget(spec_label)
        spec_layout.addWidget(self.spec_file_edit)
        spec_layout.addWidget(spec_button)
        options_layout.addLayout(spec_layout)
        
        options_group.setLayout(options_layout)
        settings_layout.addWidget(options_group)
        
        # 隐藏导入和额外参数
        advanced_group = QGroupBox('高级选项')
        advanced_layout = QVBoxLayout()
        
        # 隐藏导入
        hidden_layout = QHBoxLayout()
        hidden_label = QLabel("隐藏导入:")
        hidden_label.setMinimumWidth(80)
        self.hidden_imports_edit = QLineEdit(self.package_config.get("hidden_imports", "pandas,numpy,tkinter,sqlite3,tushare"))
        hidden_layout.addWidget(hidden_label)
        hidden_layout.addWidget(self.hidden_imports_edit)
        advanced_layout.addLayout(hidden_layout)
        
        # 数据文件
        data_layout = QHBoxLayout()
        data_label = QLabel("数据文件:")
        data_label.setMinimumWidth(80)
        self.data_files_edit = QLineEdit(self.package_config.get("data_files", ""))
        data_layout.addWidget(data_label)
        data_layout.addWidget(self.data_files_edit)
        advanced_layout.addLayout(data_layout)
        
        # 额外参数
        extra_layout = QHBoxLayout()
        extra_label = QLabel("额外参数:")
        extra_label.setMinimumWidth(80)
        self.extra_args_edit = QLineEdit(self.package_config.get("extra_args", ""))
        extra_layout.addWidget(extra_label)
        extra_layout.addWidget(self.extra_args_edit)
        advanced_layout.addLayout(extra_layout)
        
        advanced_group.setLayout(advanced_layout)
        settings_layout.addWidget(advanced_group)
        
        settings_group.setLayout(settings_layout)
        main_layout.addWidget(settings_group)
        
        # 创建按钮区域
        buttons_layout = QHBoxLayout()
        
        # 保存配置按钮
        self.save_config_button = QPushButton('保存配置')
        self.save_config_button.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                padding: 10px;
                min-width: 120px;
                font-size: 12pt;
            }
        """)
        self.save_config_button.clicked.connect(self.save_package_config)
        
        # 开始打包按钮
        self.start_package_button = QPushButton('开始打包')
        self.start_package_button.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                padding: 10px;
                min-width: 120px;
                font-size: 12pt;
            }
        """)
        self.start_package_button.clicked.connect(self.start_packaging)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_config_button)
        buttons_layout.addWidget(self.start_package_button)
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)
        
        # 创建进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel('打包进度:')
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        main_layout.addLayout(progress_layout)
        
        # 创建日志区域
        log_group = QGroupBox('打包日志')
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
        # 初始化spec文件相关控件状态
        self.toggle_spec_file()
        
        # 设置打包标签页的布局
        self.build_exe_tab.setLayout(main_layout)
        
        # 添加初始日志
        self.add_log("请设置打包参数后点击'开始打包'按钮")

    def load_package_config(self):
        """从配置文件加载打包配置"""
        config = {
            "output_dir": os.path.join(os.path.dirname(os.path.abspath(__file__)), "dist"),
            "icon_path": "",
            "python_path": sys.executable,
            "main_script": "pawencai_gui_new.py",
            "onefile": False,
            "console": False,
            "use_spec": True,
            "spec_file": "start.spec",
            "hidden_imports": "pandas,numpy,tkinter,sqlite3,tushare",
            "data_files": "",
            "extra_args": ""
        }
        
        config_file = "config.ini"
        if os.path.exists(config_file):
            try:
                # 使用configparser读取INI文件
                import configparser
                cp = configparser.ConfigParser()
                cp.read(config_file, encoding="utf-8")
                
                # 检查是否存在Package部分
                if "Package" in cp:
                    # 遍历默认配置中的所有键，尝试从配置文件中读取
                    for key in config.keys():
                        if key in cp["Package"]:
                            # 根据默认值的类型进行转换
                            if isinstance(config[key], bool):
                                config[key] = cp["Package"].getboolean(key)
                            elif isinstance(config[key], int):
                                config[key] = cp["Package"].getint(key)
                            elif isinstance(config[key], float):
                                config[key] = cp["Package"].getfloat(key)
                            else:
                                config[key] = cp["Package"].get(key)
            except Exception as e:
                self.add_log(f"加载配置文件失败: {e}")
        
        return config
    
    def save_package_config(self):
        """保存打包配置到配置文件"""
        # 收集当前设置
        config = {
            "output_dir": self.output_dir_edit.text(),
            "icon_path": self.icon_path_edit.text(),
            "python_path": self.python_path_edit.text(),
            "main_script": self.main_script_edit.text(),
            "onefile": self.onefile_check.isChecked(),
            "console": self.console_check.isChecked(),
            "use_spec": self.use_spec_check.isChecked(),
            "spec_file": self.spec_file_edit.text(),
            "hidden_imports": self.hidden_imports_edit.text(),
            "data_files": self.data_files_edit.text(),
            "extra_args": self.extra_args_edit.text()
        }
        
        # 保存到文件
        config_file = "config.ini"
        try:
            # 使用configparser写入INI文件
            import configparser
            cp = configparser.ConfigParser()
            
            # 先读取现有的配置文件，保留其他部分
            if os.path.exists(config_file):
                cp.read(config_file, encoding="utf-8")
            
            # 确保存在Package部分
            if "Package" not in cp:
                cp["Package"] = {}
            
            # 将配置写入Package部分
            for key, value in config.items():
                cp["Package"][key] = str(value)
            
            # 保存到文件
            with open(config_file, "w", encoding="utf-8") as f:
                cp.write(f)
                
            self.add_log("配置已保存")
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            self.add_log(f"保存配置文件失败: {e}")
            QMessageBox.critical(self, "错误", f"保存配置文件失败: {e}")
    
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, '选择输出目录', self.output_dir_edit.text())
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def select_icon(self):
        """选择图标文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择图标文件', self.icon_path_edit.text(), '图标文件 (*.ico)')
        if file_path:
            self.icon_path_edit.setText(file_path)
    
    def select_python_path(self):
        """选择Python解释器路径"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择Python解释器', self.python_path_edit.text(), 'Python解释器 (python.exe)')
        if file_path:
            self.python_path_edit.setText(file_path)
    
    def select_main_script(self):
        """选择主脚本文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择主脚本文件', self.main_script_edit.text(), 'Python文件 (*.py)')
        if file_path:
            self.main_script_edit.setText(file_path)
    
    def select_spec_file(self):
        """选择spec文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择Spec文件', self.spec_file_edit.text(), 'Spec文件 (*.spec)')
        if file_path:
            self.spec_file_edit.setText(file_path)
    
    def toggle_spec_file(self):
        """根据是否使用spec文件切换相关控件的状态"""
        use_spec = self.use_spec_check.isChecked()
        self.spec_file_edit.setEnabled(use_spec)
        
        # 如果使用spec文件，则禁用一些选项
        self.onefile_check.setEnabled(not use_spec)
        self.console_check.setEnabled(not use_spec)
        self.hidden_imports_edit.setEnabled(not use_spec)
        self.data_files_edit.setEnabled(not use_spec)
        self.extra_args_edit.setEnabled(not use_spec)
    
    def add_log(self, message):
        """添加日志"""
        # 使用信号槽机制在主线程中更新UI
        current_time = time.strftime('%H:%M:%S')
        log_message = f"[{current_time}] {message}"
        self.log_signal.emit(log_message)
    
    def update_log_text(self, message):
        """在主线程中更新日志文本"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def start_packaging(self):
        """开始打包过程"""
        # 禁用按钮
        self.start_package_button.setEnabled(False)
        self.save_config_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 收集当前设置
        config = {
            "output_dir": self.output_dir_edit.text(),
            "icon_path": self.icon_path_edit.text(),
            "python_path": self.python_path_edit.text(),
            "main_script": self.main_script_edit.text(),
            "onefile": self.onefile_check.isChecked(),
            "console": self.console_check.isChecked(),
            "use_spec": self.use_spec_check.isChecked(),
            "spec_file": self.spec_file_edit.text(),
            "hidden_imports": self.hidden_imports_edit.text(),
            "data_files": self.data_files_edit.text(),
            "extra_args": self.extra_args_edit.text()
        }
        
        # 验证必要的设置
        if not config["main_script"]:
            QMessageBox.warning(self, "警告", "请选择主脚本文件")
            self.start_package_button.setEnabled(True)
            self.save_config_button.setEnabled(True)
            return
        
        if not os.path.exists(config["main_script"]):
            QMessageBox.warning(self, "警告", f"主脚本文件不存在: {config['main_script']}")
            self.start_package_button.setEnabled(True)
            self.save_config_button.setEnabled(True)
            return
        
        if config["use_spec"] and not os.path.exists(config["spec_file"]):
            QMessageBox.warning(self, "警告", f"Spec文件不存在: {config['spec_file']}")
            self.start_package_button.setEnabled(True)
            self.save_config_button.setEnabled(True)
            return
        
        # 保存当前配置
        self.save_package_config()
        
        # 创建输出目录
        if not os.path.exists(config["output_dir"]):
            try:
                os.makedirs(config["output_dir"])
            except Exception as e:
                self.add_log(f"创建输出目录失败: {e}")
                QMessageBox.critical(self, "错误", f"创建输出目录失败: {e}")
                self.start_package_button.setEnabled(True)
                self.save_config_button.setEnabled(True)
                return
        
        # 创建并启动打包线程
        self.add_log(f"开始打包 {config['main_script']}...")
        
        # 创建打包线程
        self.packaging_thread = PackagingThread(config)
        
        # 连接信号
        self.packaging_thread.signals.log_message.connect(self.add_log)
        self.packaging_thread.signals.update_progress.connect(self.progress_bar.setValue)
        self.packaging_thread.signals.finished.connect(self.packaging_finished)
        self.packaging_thread.signals.error.connect(self.packaging_error)
        
        # 启动线程
        self.packaging_thread.start()
    
    def packaging_finished(self):
        """打包完成时的回调函数"""
        # 重新启用按钮
        self.start_package_button.setEnabled(True)
        self.save_config_button.setEnabled(True)
        
        # 显示成功消息
        output_dir = self.output_dir_edit.text()
        QMessageBox.information(self, "成功", f"打包成功\n输出目录: {output_dir}")
    
    def packaging_error(self, error_message):
        """打包错误时的回调函数"""
        # 重新启用按钮
        self.start_package_button.setEnabled(True)
        self.save_config_button.setEnabled(True)
        
        # 显示错误消息
        QMessageBox.critical(self, "错误", error_message)
    
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, '选择输出目录', self.output_dir_edit.text())
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def closeEvent(self, event):
        """关闭窗口时的处理"""
        if self.process:
            reply = QMessageBox.question(
                self, '确认退出',
                '任务正在进行中，确定要退出吗？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

# 全局信号对象，用于安全地更新UI
global_signals = WorkerSignals()

# 安全地将文本追加到QTextEdit
def safe_append_to_text_edit(text, text_edit):
    """安全地将文本追加到QTextEdit，避免QTextCursor线程安全问题"""
    if text and text_edit:
        text_edit.append(text)
        # 滚动到底部
        text_edit.verticalScrollBar().setValue(
            text_edit.verticalScrollBar().maximum())

# 连接信号到处理函数
global_signals.safe_append_text.connect(safe_append_to_text_edit)

if __name__ == '__main__':
    app = QApplication(sys.argv)

    window = PawencaiGUI()
    window.show()
    sys.exit(app.exec_())
