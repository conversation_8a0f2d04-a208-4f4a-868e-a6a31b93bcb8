import configparser
import os

def get_db_path():
    """
    统一获取数据库路径的函数
    优先从config.ini中读取，如果没有配置则使用当前目录的stock.db
    """
    config_path = 'config.ini'
    if not os.path.exists(config_path):
        # 尝试上级目录
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(parent_dir, 'config.ini')
    
    db_path = None
    if os.path.exists(config_path):
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        if config.has_section('Path') and config.has_option('Path', 'db_path'):
            db_path = config.get('Path', 'db_path')
    
    if not db_path:
        # 默认路径：当前目录下的stock.db
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'file', 'stock.db')
    
    return db_path 