import sys
import os
import sqlite3
import configparser
import tushare as ts
import pandas as pd
import random
import time
import datetime
import json
import threading
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from threading import Semaphore
from db_utils import DbUtils
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QProgressBar, QTextEdit, QMessageBox, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QObject

class WorkerSignals(QObject):
    """定义工作线程的信号"""
    update_log = pyqtSignal(str)
    update_progress = pyqtSignal(int, str)
    finished = pyqtSignal()
    error = pyqtSignal(str)
    status_update_signal = pyqtSignal(str)  # 添加状态更新信号，用于与主界面状态栏通信

class ThsConceptUpdater(QWidget):
    # 添加状态更新信号，用于与主界面状态栏通信
    status_update_signal = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 读取配置文件
        self.config = configparser.ConfigParser()
        self.config.read('config.ini', encoding='utf-8')

        # 获取tushare token
        try:
            # 首先尝试从API部分获取token
            self.token = self.config.get('API', 'token')
        except configparser.NoSectionError:
            # 如果API部分不存在，尝试从Tushare部分获取
            try:
                self.token = self.config.get('Tushare', 'token')
            except configparser.NoSectionError:
                QMessageBox.critical(self, "错误", "请在config.ini文件中配置有效的Tushare token")
                return

        # 验证token是否有效
        if self.token == 'your_tushare_token_here':
            QMessageBox.critical(self, "错误", "请在config.ini文件中配置有效的Tushare token")
            return

        # 初始化tushare接口
        ts.set_token(self.token)
        try:
            # 设置超时时间和重试次数
            self.pro = ts.pro_api(timeout=30)
            # 测试token是否有效
            try:
                self.pro.index_basic(market='SW')
            except Exception as e:
                # 如果是网络错误，尝试使用不同的连接方式
                if "HTTPConnectionPool" in str(e) or "WinError 10048" in str(e):
                    print("网络连接错误，尝试使用备用连接方式...")
                    # 尝试使用不同的连接方式
                    self.pro = ts.pro_api(timeout=60, pause=1)
                    # 再次测试
                    self.pro.index_basic(market='SW')
                else:
                    raise e
        except Exception as e:
            QMessageBox.critical(self, "错误", f"Tushare token无效或网络错误: {str(e)}")
            return

        # 断点续传文件
        self.checkpoint_file = 'ths_updater_checkpoint.json'
        self.current_task = None
        self.current_index = 0
        self.total_items = 0

        # 创建界面
        self.create_widgets()

        # 数据库连接将在每个线程中单独创建
        self.conn = None

        # 创建工作线程信号
        self.signals = WorkerSignals()
        self.signals.update_log.connect(self.log)
        self.signals.update_progress.connect(self.update_progress)
        self.signals.error.connect(self.show_error)

        # 检查是否有断点续传
        self.check_checkpoint()

        # 添加全局错误计数和自动保护机制
        self.global_error_count = 0
        self.global_error_threshold = 20  # 如果连续20次错误，触发全局保护机制
        self.global_error_lock = threading.Lock()

    def create_widgets(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建按钮框架
        btn_frame = QHBoxLayout()

        # 创建按钮
        button_width = 250  # 大幅增大按钮宽度
        button_height = 80  # 大幅增大按钮高度
        button_style = """
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-size: 24px;  /* 大幅增大字体大小 */
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """
        
        # 测试按钮
        self.test_btn = QPushButton("测试5只随机概念")
        self.test_btn.clicked.connect(self.test_random_stocks)
        self.test_btn.setFixedSize(button_width, button_height)
        self.test_btn.setStyleSheet(button_style)
        btn_frame.addWidget(self.test_btn)
        
        # 更新概念列表按钮
        self.update_concept_btn = QPushButton("更新概念列表")
        self.update_concept_btn.clicked.connect(self.update_concept_list)
        self.update_concept_btn.setFixedSize(button_width, button_height)
        self.update_concept_btn.setStyleSheet(button_style)
        btn_frame.addWidget(self.update_concept_btn)

        # 更新成分个股按钮
        self.update_members_btn = QPushButton("更新成分个股")
        self.update_members_btn.clicked.connect(self.update_concept_members)
        self.update_members_btn.setFixedSize(button_width, button_height)
        self.update_members_btn.setStyleSheet(button_style)
        btn_frame.addWidget(self.update_members_btn)
        
        # 添加断点续传按钮
        self.resume_btn = QPushButton("断点续传")
        self.resume_btn.clicked.connect(self.resume_from_checkpoint)
        self.resume_btn.setEnabled(False)  # 默认禁用
        self.resume_btn.setFixedSize(button_width, button_height)
        self.resume_btn.setStyleSheet(button_style)
        btn_frame.addWidget(self.resume_btn)
        
        # 设置按钮之间的间距
        btn_frame.setSpacing(15)  # 设置按钮之间的间距为15像素

        main_layout.addLayout(btn_frame)

        # 创建进度条框架
        progress_frame = QHBoxLayout()

        self.progress_label = QLabel("准备就绪")
        # 不设置最小宽度
        # 不设置特殊字体
        progress_frame.addWidget(self.progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        # 不设置特殊高度和样式
        progress_frame.addWidget(self.progress_bar)

        self.progress_percent = QLabel("0%")
        # 不设置最小宽度和字体
        progress_frame.addWidget(self.progress_percent)

        main_layout.addLayout(progress_frame)

        # 创建日志区域
        log_group = QGroupBox("操作日志")
        # 不设置特殊组框字体
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 不设置特殊高度和字体
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置主布局
        self.setLayout(main_layout)

    def check_checkpoint(self):
        """检查是否有断点续传数据，但不自动弹出询问框"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint = json.load(f)

                self.current_task = checkpoint.get('task')
                self.current_index = checkpoint.get('index', 0)
                self.total_items = checkpoint.get('total', 0)

                if self.current_task and self.current_index < self.total_items:
                    msg = f"发现未完成的任务: {self.current_task}，已完成 {self.current_index}/{self.total_items}"
                    self.log(msg)

                    # 更新进度条
                    if self.total_items > 0:
                        progress = (self.current_index / self.total_items) * 100
                        self.update_progress(progress, msg)
                        
                    # 启用断点续传按钮
                    self.resume_btn.setEnabled(True)
            except Exception as e:
                self.log(f"读取断点续传数据失败: {str(e)}")
                self.reset_checkpoint()
        else:
            # 如果没有断点续传数据，禁用断点续传按钮
            self.resume_btn.setEnabled(False)

    def save_checkpoint(self, task, index, total):
        """保存断点续传数据"""
        checkpoint = {
            'task': task,
            'index': index,
            'total': total,
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存断点续传数据失败: {str(e)}")

    def reset_checkpoint(self):
        """重置断点续传数据"""
        self.current_task = None
        self.current_index = 0
        self.total_items = 0
        
        if os.path.exists(self.checkpoint_file):
            try:
                os.remove(self.checkpoint_file)
            except Exception as e:
                self.log(f"删除断点续传文件失败: {str(e)}")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        # 在UI线程中更新日志
        self.log_text.append(log_message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
        # 发送状态更新信号
        self.status_update_signal.emit(message)

    def update_progress(self, value, message=""):
        """更新进度条"""
        self.progress_bar.setValue(int(value))
        self.progress_percent.setText(f"{int(value)}%")
        if message:
            self.progress_label.setText(message)

    def show_error(self, error_message):
        """显示错误信息"""
        self.log(f"错误: {error_message}")
        QMessageBox.critical(self, "错误", error_message)

    def test_random_stocks(self):
        """测试随机5只概念股票"""
        self.log("开始测试随机5只概念股票...")
        
        # 创建工作线程
        worker_thread = threading.Thread(target=self._test_random_stocks_impl)
        worker_thread.daemon = True
        worker_thread.start()

    def _test_random_stocks_impl(self):
        """测试随机5只概念股票的实现方法"""
        try:
            # 在工作线程中创建数据库连接
            # 使用DbUtils获取数据库路径
            db_paths = DbUtils.get_db_paths()
            # 连接到第一个数据库路径来获取数据
            conn = sqlite3.connect(db_paths[0])
            cursor = conn.cursor()
            
            # 获取所有概念列表
            cursor.execute("SELECT name FROM ths_concepts")
            concepts = cursor.fetchall()
            
            if not concepts:
                self.signals.error.emit("没有找到任何概念数据，请先更新概念列表")
                return
            
            # 随机选择5个概念
            selected_concepts = random.sample(concepts, min(5, len(concepts)))
            
            self.signals.update_log.emit(f"随机选择了 {len(selected_concepts)} 个概念进行测试")
            
            for i, (concept_name,) in enumerate(selected_concepts):
                # 更新进度
                progress = ((i + 1) / len(selected_concepts)) * 100
                self.signals.update_progress.emit(progress, f"测试概念 {i+1}/{len(selected_concepts)}")
                
                self.signals.update_log.emit(f"概念: {concept_name}")
                
                # 首先查询概念代码和成分股数量
                cursor.execute("""
                    SELECT ts_code, count FROM ths_concepts
                    WHERE name = ?
                """, (concept_name,))
                
                concept_info = cursor.fetchone()
                if not concept_info:
                    self.signals.update_log.emit(f"  找不到概念信息: {concept_name}")
                    continue
                    
                concept_code, count = concept_info
                # 处理count可能为None的情况
                count_value = 0 if count is None else int(count)
                self.signals.update_log.emit(f"  概念代码: {concept_code}, 成分股数量: {count_value}")
                
                # 添加调试信息，查询ths_concept_members表中的一条数据作为样本
                cursor.execute("SELECT * FROM ths_concept_members LIMIT 1")
                sample_row = cursor.fetchone()
                if sample_row:
                    self.signals.update_log.emit(f"  样本数据: {sample_row}")
                    # 显示列名
                    cursor.execute("PRAGMA table_info(ths_concept_members)")
                    columns = [col[1] for col in cursor.fetchall()]
                    self.signals.update_log.emit(f"  表结构: {columns}")
                
                # 尝试不同的查询方式
                self.signals.update_log.emit(f"  尝试使用con_code={concept_code}查询...")
                cursor.execute("""
                    SELECT ts_code, con_name FROM ths_concept_members
                    WHERE con_code = ?
                    LIMIT 5
                """, (concept_code,))
                
                # 保存第一次查询结果
                stocks_by_con_code = cursor.fetchall()
                
                # 如果没有找到，尝试使用ts_code查询
                if not stocks_by_con_code:
                    self.signals.update_log.emit(f"  尝试使用ts_code={concept_code}查询...")
                    cursor.execute("""
                        SELECT ts_code, con_name FROM ths_concept_members
                        WHERE ts_code = ?
                        LIMIT 5
                    """, (concept_code,))
                    
                    # 保存第二次查询结果
                    stocks_by_ts_code = cursor.fetchall()
                    
                    # 使用第二次查询结果
                    if stocks_by_ts_code:
                        for ts_code, stock_name in stocks_by_ts_code:
                            self.signals.update_log.emit(f"  - {stock_name}({ts_code})")
                    else:
                        self.signals.update_log.emit("  没有找到成分股")
                else:
                    # 使用第一次查询结果
                    if stocks_by_con_code:
                        for ts_code, stock_name in stocks_by_con_code:
                            self.signals.update_log.emit(f"  - {stock_name}({ts_code})")
                    else:
                        self.signals.update_log.emit("  没有找到成分股")
                
                # 暂停一下，避免界面刷新太快
                time.sleep(0.5)
            
            # 关闭数据库连接
            conn.close()
            
            self.signals.update_log.emit("测试完成")
            self.signals.finished.emit()
            
        except Exception as e:
            self.signals.error.emit(f"测试失败: {str(e)}")

    def update_concept_list(self):
        """更新同花顺概念列表"""
        self.log("开始更新同花顺概念列表...")
        
        # 创建工作线程
        worker_thread = threading.Thread(target=self._update_concept_list_impl)
        worker_thread.daemon = True
        worker_thread.start()

    def _update_concept_list_impl(self):
        """更新同花顺概念列表的实现方法"""
        try:
            # 获取数据库路径
            db_path = None
            try:
                config = configparser.ConfigParser()
                config.read('config.ini', encoding='utf-8')
                db_path = config.get('Database', 'path')
            except:
                db_path = "stock.db"
            
            # 确保数据库路径有.db后缀
            if not db_path.endswith('.db'):
                db_path = f"{db_path}.db"
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 创建同花顺概念表（如果不存在）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ths_concept (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                con_code TEXT NOT NULL,
                con_name TEXT NOT NULL,
                update_time TEXT,
                UNIQUE(con_code)
            )
            """)
            
            # 获取同花顺概念列表
            self.signals.update_log.emit("正在获取同花顺概念列表...")
            
            # 这里添加实际的获取同花顺概念列表的代码
            # 由于没有直接的API，我们可以使用预定义的列表或者其他方式
            
            # 示例数据 - 实际应用中应替换为真实数据
            concepts = [
                {"con_code": "301001", "con_name": "5G概念"},
                {"con_code": "301002", "con_name": "人工智能"},
                {"con_code": "301003", "con_name": "区块链"},
                {"con_code": "301004", "con_name": "云计算"},
                {"con_code": "301005", "con_name": "大数据"},
                # 添加更多概念...
            ]
            
            # 更新数据库
            update_count = 0
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            for concept in concepts:
                try:
                    cursor.execute("""
                    INSERT OR REPLACE INTO ths_concept (con_code, con_name, update_time)
                    VALUES (?, ?, ?)
                    """, (concept["con_code"], concept["con_name"], current_time))
                    update_count += 1
                except Exception as e:
                    self.signals.update_log.emit(f"插入概念 {concept['con_name']} 时出错: {str(e)}")
            
            # 提交事务
            conn.commit()
            
            # 关闭数据库连接
            conn.close()
            
            self.signals.update_log.emit(f"同花顺概念列表更新完成，共更新 {update_count} 条数据")
            self.signals.finished.emit()
            
        except Exception as e:
            self.signals.error.emit(f"更新同花顺概念列表失败: {str(e)}")

    def resume_from_checkpoint(self):
        """从断点处继续任务"""
        if not os.path.exists(self.checkpoint_file):
            self.log("没有找到断点续传数据")
            return
            
        try:
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)

            self.current_task = checkpoint.get('task')
            self.current_index = checkpoint.get('index', 0)
            self.total_items = checkpoint.get('total', 0)
            
            if self.current_task == "update_concept_members":
                reply = QMessageBox.question(
                    self, "断点续传",
                    f"是否继续上次未完成的任务: {self.current_task}?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    self.update_concept_members(resume=True)
            else:
                self.log(f"未知的任务类型: {self.current_task}")
        except Exception as e:
            self.log(f"读取断点续传数据失败: {str(e)}")
    
    def update_concept_members(self, resume=False):
        """更新同花顺概念成分股"""
        if resume:
            self.log(f"继续更新同花顺概念成分股，从第 {self.current_index+1} 个概念开始...")
        else:
            self.log("开始更新同花顺概念成分股...")
            # 重置断点续传
            self.reset_checkpoint()
        
        # 创建并启动工作线程
        worker_thread = threading.Thread(target=lambda: self._update_concept_members_impl(resume))
        worker_thread.daemon = True
        worker_thread.start()

    def _update_concept_members_impl(self, resume=False):
        """更新同花顺概念成分股的实现方法"""
        # 记录当前任务
        self.current_task = "update_concept_members"
        
        try:
            # 获取数据库路径
            db_path = None
            try:
                config = configparser.ConfigParser()
                config.read('config.ini', encoding='utf-8')
                if config.has_section('Path') and config.has_option('Path', 'db_path'):
                    db_path = config.get('Path', 'db_path')
            except:
                pass
            
            if not db_path:
                db_path = DbUtils.get_db_paths()[0]
            
            # 创建数据库连接
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有概念列表
            cursor.execute("SELECT ts_code, name FROM ths_concepts")
            concepts = cursor.fetchall()
            
            if not concepts:
                self.signals.error.emit("没有找到任何概念数据，请先更新概念列表")
                conn.close()
                return
            
            # 创建成分股表（如果不存在）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ths_concept_members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                con_code TEXT NOT NULL,
                con_name TEXT NOT NULL,
                ts_code TEXT NOT NULL,
                in_date TEXT,
                out_date TEXT,
                is_new TEXT,
                weight REAL,
                update_time TEXT NOT NULL,
                UNIQUE(con_code, ts_code)
            )
            """)
            conn.commit()
            conn.close()  # 关闭主连接，让每个线程创建自己的连接
            
            # 引入并发处理库
            from concurrent.futures import ThreadPoolExecutor
            import concurrent.futures
            
            # 不再使用严格的API速率控制，直接通过线程数控制
            
            # 初始化变量
            total_items = len(concepts)
            self.total_items = total_items
            self.current_task = "update_concept_members"
            start_index = self.current_index if resume else 0
            start_time = time.time()
            
            # 添加动态延迟控制参数
            self.api_delay = 1.0  # 初始延迟时间增加到1.0秒
            self.error_count = 0  # 错误计数
            self.error_threshold = 3  # 错误阈值，达到时增加延迟
            self.delay_lock = threading.Lock()  # 用于保护延迟参数

            # 使用列表以便在线程间共享，记录总共更新的股票数
            self.total_updated = 0
            # 使用线程锁保护共享变量
            self.update_lock = threading.Lock()
            # 记录完成状态
            self._job_completed = False
            
            # 进度状态更新线程
            def update_status():
                last_index = 0
                while not self._job_completed:
                    if hasattr(self, 'current_processed') and self.current_processed > last_index:
                        last_index = self.current_processed
                        progress = (self.current_processed / total_items) * 100
                        
                        # 计算预计剩余时间
                        if self.current_processed > start_index:
                            elapsed = time.time() - start_time
                            concepts_per_second = (self.current_processed - start_index) / elapsed if elapsed > 0 else 0
                            remaining_concepts = total_items - self.current_processed
                            remaining_time = remaining_concepts / concepts_per_second if concepts_per_second > 0 else 0
                            
                            # 格式化剩余时间
                            if remaining_time < 60:
                                time_str = f"{int(remaining_time)}秒"
                            elif remaining_time < 3600:
                                time_str = f"{int(remaining_time/60)}分{int(remaining_time%60)}秒"
                            else:
                                time_str = f"{int(remaining_time/3600)}时{int((remaining_time%3600)/60)}分"
                            
                            progress_msg = f"更新概念成分股 {self.current_processed}/{total_items} (预计剩余: {time_str})"
                            self.signals.update_progress.emit(progress, progress_msg)
                    
                    time.sleep(1)  # 每秒更新一次状态
            
            # 初始化进度计数器
            self.current_processed = start_index
            
            # 启动状态更新线程
            status_thread = threading.Thread(target=update_status)
            status_thread.daemon = True
            status_thread.start()
            
            # 用于定期保存断点的计数器和锁
            self.checkpoint_counter = 0
            self.checkpoint_lock = threading.Lock()
            
            # 处理单个概念的函数
            def process_concept(idx_concept):
                idx, concept = idx_concept
                concept_code, concept_name = concept
                
                if idx < start_index:
                    return 0  # 跳过已处理的概念
                
                # 添加随机起始延迟，避免同时发起请求
                time.sleep(random.uniform(0.1, 0.5))
                
                # 创建独立的数据库连接
                local_conn = sqlite3.connect(db_path)
                local_cursor = local_conn.cursor()
                
                # 获取当前延迟值
                with self.delay_lock:
                    current_delay = self.api_delay
                
                try:
                    # 减少日志输出频率避免界面卡顿
                    with self.update_lock:
                        should_log = idx % 10 == 0
                    
                    if should_log:
                        self.signals.update_log.emit(f"正在获取概念 [{concept_name}] 的成分股... (当前API延迟: {current_delay:.1f}秒)")
                    
                    # 使用当前延迟时间
                    df = self.pro.ths_member(ts_code=concept_code)
                    time.sleep(current_delay)
                    
                    # 成功获取数据，可以考虑稍微减少延迟
                    with self.delay_lock:
                        self.error_count = 0  # 重置错误计数
                        # 重置全局错误计数
                        with self.global_error_lock:
                            self.global_error_count = 0
                        # 偶尔尝试减少延迟，但不低于基础值
                        if random.random() < 0.05 and self.api_delay > 1.0:  # 降低减速概率，延迟不低于1.0秒
                            self.api_delay = max(1.0, self.api_delay * 0.95)  # 每次只减少5%
                    
                    update_count = 0
                    if not df.empty:
                        # 当前时间
                        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 批量插入准备
                        rows_to_insert = []
                        for _, row in df.iterrows():
                            ts_code = row['ts_code']
                            con_name = row.get('con_name', '')
                            in_date = row.get('in_date', '')
                            out_date = row.get('out_date', '')
                            is_new = row.get('is_new', '')
                            weight = row.get('weight', 0)
                            
                            # 收集插入数据
                            rows_to_insert.append(
                                (concept_code, concept_name, ts_code, in_date, out_date, is_new, weight, now)
                            )
                        
                        # 批量插入数据库
                        local_cursor.executemany("""
                        INSERT OR REPLACE INTO ths_concept_members 
                        (con_code, con_name, ts_code, in_date, out_date, is_new, weight, update_time) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, rows_to_insert)
                        local_conn.commit()
                        
                        update_count = len(rows_to_insert)
                        
                        # 更新总计数
                        with self.update_lock:
                            self.total_updated += update_count
                            self.current_processed += 1
                            
                            # 定期保存断点
                            self.checkpoint_counter += 1
                            if self.checkpoint_counter % 10 == 0:  # 每处理10个概念保存一次
                                self.current_index = idx
                                self.save_checkpoint("update_concept_members", idx, total_items)
                    else:
                        # 即使没有数据也更新处理计数
                        with self.update_lock:
                            self.current_processed += 1
                    
                    return update_count
                    
                except Exception as e:
                    error_msg = str(e)
                    if "超过调用" in error_msg or "max retries" in error_msg.lower():
                        # 全局错误计数增加
                        with self.global_error_lock:
                            self.global_error_count += 1
                            # 如果全局错误达到阈值，强制大幅增加延迟
                            if self.global_error_count >= self.global_error_threshold:
                                self.api_delay = max(3.0, self.api_delay * 2)  # 翻倍当前延迟，且至少3秒
                                self.signals.update_log.emit(f"检测到大量API限制错误，触发全局保护机制，延迟增加到 {self.api_delay:.1f}秒")
                                self.global_error_count = 0  # 重置全局错误计数
                                time.sleep(30)  # 强制暂停30秒让API冷却
                        
                        # 增加错误计数并调整延迟
                        with self.delay_lock:
                            self.error_count += 1
                            # 当错误达到阈值时，增加延迟
                            if self.error_count >= self.error_threshold:
                                self.api_delay = min(5.0, self.api_delay * 1.5)  # 增加50%的延迟，但不超过5秒
                                self.signals.update_log.emit(f"检测到频繁API限制，增加延迟到 {self.api_delay:.1f}秒")
                                self.error_count = 0  # 重置计数
                        
                        # 使用更长的等待时间
                        wait_time = self.api_delay * 2
                        self.signals.update_log.emit(f"API限制错误，等待{wait_time:.1f}秒后重试: {error_msg}")
                        time.sleep(wait_time)
                        
                        # 尝试重新请求一次
                        try:
                            self.signals.update_log.emit(f"重试获取概念 [{concept_name}] 的成分股...")
                            df = self.pro.ths_member(ts_code=concept_code)
                            time.sleep(current_delay)
                            
                            update_count = 0
                            if not df.empty:
                                # 处理数据...（与上面相同的处理逻辑）
                                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                rows_to_insert = []
                                for _, row in df.iterrows():
                                    ts_code = row['ts_code']
                                    con_name = row.get('con_name', '')
                                    in_date = row.get('in_date', '')
                                    out_date = row.get('out_date', '')
                                    is_new = row.get('is_new', '')
                                    weight = row.get('weight', 0)
                                    
                                    rows_to_insert.append(
                                        (concept_code, concept_name, ts_code, in_date, out_date, is_new, weight, now)
                                    )
                                
                                local_cursor.executemany("""
                                INSERT OR REPLACE INTO ths_concept_members 
                                (con_code, con_name, ts_code, in_date, out_date, is_new, weight, update_time) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                """, rows_to_insert)
                                local_conn.commit()
                                
                                update_count = len(rows_to_insert)
                                
                                with self.update_lock:
                                    self.total_updated += update_count
                                    self.current_processed += 1
                                    
                                    self.checkpoint_counter += 1
                                    if self.checkpoint_counter % 10 == 0:
                                        self.current_index = idx
                                        self.save_checkpoint("update_concept_members", idx, total_items)
                                
                                return update_count
                        except Exception as retry_error:
                            self.signals.update_log.emit(f"重试获取概念 [{concept_name}] 时再次失败: {str(retry_error)}")
                    else:
                        self.signals.update_log.emit(f"处理概念 [{concept_name}] 时出错: {error_msg}")
                    
                    # 即使出错也更新处理计数
                    with self.update_lock:
                        self.current_processed += 1
                    
                    return 0
                finally:
                    # 确保关闭连接
                    local_cursor.close()
                    local_conn.close()
            
            # 准备概念处理列表
            indexed_concepts = list(enumerate(concepts))
            if start_index > 0:
                # 如果是续传，确保从正确位置开始
                indexed_concepts = indexed_concepts[start_index:]
            
            # 使用线程池并行处理 - 使用2个线程并发处理
            with ThreadPoolExecutor(max_workers=2) as executor:
                # 不要一次提交所有任务，而是分批提交以控制速率
                self.signals.update_log.emit(f"开始分批处理概念成分股数据，共 {len(indexed_concepts)} 个概念...")
                
                # 创建一个队列来存储所有的future对象
                all_futures = []
                batch_size = 10  # 每批提交10个任务
                
                for i in range(0, len(indexed_concepts), batch_size):
                    # 更新当前进度并保存断点
                    self.current_index = i
                    self.total_items = len(indexed_concepts)
                    self.save_checkpoint("update_concept_members", i, len(indexed_concepts))
                    
                    # 更新进度条
                    progress = (i / len(indexed_concepts)) * 100
                    self.signals.update_progress.emit(int(progress), f"处理概念 {i}/{len(indexed_concepts)}")
                    
                    batch = indexed_concepts[i:i+batch_size]
                    self.signals.update_log.emit(f"提交第 {i//batch_size+1} 批任务，包含 {len(batch)} 个概念")
                    
                    # 提交这一批任务
                    batch_futures = [executor.submit(process_concept, item) for item in batch]
                    all_futures.extend(batch_futures)
                    
                    # 等待这一批任务完成一部分再提交下一批
                    # 这样可以避免短时间内提交太多请求
                    if i + batch_size < len(indexed_concepts):
                        # 等待当前批次的一部分任务完成
                        time.sleep(5)  # 每批之间增加5秒的间隔
                
                # 等待所有任务完成
                concurrent.futures.wait(all_futures)
            
            # 标记任务完成
            self._job_completed = True
            
            # 计算总耗时
            total_time = time.time() - start_time
            if total_time < 60:
                time_str = f"{total_time:.1f}秒"
            elif total_time < 3600:
                time_str = f"{int(total_time/60)}分{int(total_time%60)}秒"
            else:
                time_str = f"{int(total_time/3600)}时{int((total_time%3600)/60)}分{int(total_time%60)}秒"
            
            # 重置断点续传
            self.reset_checkpoint()
            
            # 显示统计结果
            self.signals.update_log.emit(f"所有同花顺概念成分股更新完成，共更新 {self.total_updated} 只股票，耗时 {time_str}")
            self.signals.finished.emit()
            
        except Exception as e:
            self.signals.error.emit(f"更新同花顺概念成分股失败: {str(e)}")

class StockUpdater(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("同花顺概念数据更新工具")
        self.resize(800, 600)  # 恢复原始窗口尺寸
        
        # 创建主布局
        main_layout = QVBoxLayout()
        
        # 创建同花顺概念更新器
        self.ths_updater = ThsConceptUpdater(self)
        main_layout.addWidget(self.ths_updater)
        
        # 设置主布局
        self.setLayout(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
        QWidget {
            font-family: Arial, sans-serif;
            font-size: 12px;  # 恢复原始字体大小
        }
        QGroupBox {
            border: 1px solid #cccccc;
            border-radius: 4px;
            margin-top: 12px;
            font-weight: bold;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
        }
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 4px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #4a86e8;
            width: 10px;
        }
        QTextEdit {
            border: 1px solid #cccccc;
            border-radius: 4px;
        }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    updater = StockUpdater()
    updater.show()
    sys.exit(app.exec_())


