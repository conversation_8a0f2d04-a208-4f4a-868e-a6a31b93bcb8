import os
import time
import sqlite3
import pandas as pd
import tushare as ts
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QLineEdit, QTextEdit, QGroupBox, QMessageBox, QTableWidget,
                           QTableWidgetItem, QHeaderView, QComboBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QObject

class StockValidatorSignals(QObject):
    """
    定义股票验证器的信号
    """
    update_log = pyqtSignal(str)
    update_result = pyqtSignal(dict)
    error = pyqtSignal(str)

class StockValidatorWidget(QWidget):
    """股票数据验证界面，用于验证和比较不同来源的股票数据"""

    def __init__(self):
        super().__init__()
        self.initUI()
        self.token = self._get_token()
        self.pro = None
        if self.token:
            ts.set_token(self.token)
            self.pro = ts.pro_api()

    def _get_token(self):
        """从配置文件中获取Tushare API token"""
        config_path = 'config.ini'
        if not os.path.exists(config_path):
            return None

        try:
            import configparser
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            token = config.get('API', 'token')
            return token
        except Exception as e:
            print(f"读取配置文件出错: {str(e)}")
            return None

    def initUI(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建标题
        title_label = QLabel('股票数据验证工具')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建股票代码输入区域
        input_group = QGroupBox('股票代码输入')
        input_layout = QHBoxLayout()

        self.code_label = QLabel('股票代码:')
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText('输入股票代码，如: 600828')
        # 添加回车响应
        self.code_edit.returnPressed.connect(self.validate_stock_data)

        self.validate_button = QPushButton('验证数据')
        self.validate_button.clicked.connect(self.validate_stock_data)

        input_layout.addWidget(self.code_label)
        input_layout.addWidget(self.code_edit)
        input_layout.addWidget(self.validate_button)

        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 创建数据源选择区域
        source_group = QGroupBox('数据源选择')
        source_layout = QHBoxLayout()

        self.db_checkbox = QCheckBox('本地数据库')
        self.db_checkbox.setChecked(True)
        self.tushare_checkbox = QCheckBox('Tushare API')
        self.tushare_checkbox.setChecked(True)
        # 移除通达信估算选项
        self.tdx_checkbox = QCheckBox('通达信估算')
        self.tdx_checkbox.setChecked(False)
        self.tdx_checkbox.setVisible(False)  # 隐藏此选项

        source_layout.addWidget(self.db_checkbox)
        source_layout.addWidget(self.tushare_checkbox)
        # source_layout.addWidget(self.tdx_checkbox)  # 注释掉，不添加到界面

        source_group.setLayout(source_layout)
        main_layout.addWidget(source_group)

        # 创建数据库设置区域
        db_group = QGroupBox('数据库设置')
        db_layout = QHBoxLayout()

        self.db_path_label = QLabel('数据库文件:')
        self.db_path_edit = QLineEdit('stock.db')
        # 添加回车响应
        self.db_path_edit.returnPressed.connect(self.validate_stock_data)

        self.table_label = QLabel('数据表:')
        self.table_edit = QLineEdit('stocks')
        # 添加回车响应
        self.table_edit.returnPressed.connect(self.validate_stock_data)

        db_layout.addWidget(self.db_path_label)
        db_layout.addWidget(self.db_path_edit)
        db_layout.addWidget(self.table_label)
        db_layout.addWidget(self.table_edit)

        db_group.setLayout(db_layout)
        main_layout.addWidget(db_group)

        # 创建结果显示表格
        result_group = QGroupBox('验证结果')
        result_layout = QVBoxLayout()

        self.result_table = QTableWidget(0, 4)  # 4列：数据项、本地数据库、Tushare API、差异
        self.result_table.setHorizontalHeaderLabels(['数据项', '本地数据库', 'Tushare API', '差异说明'])
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        result_layout.addWidget(self.result_table)
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)

        # 创建日志区域
        log_group = QGroupBox('操作日志')
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置布局
        self.setLayout(main_layout)

        # 初始化信号
        self.signals = StockValidatorSignals()
        self.signals.update_log.connect(self.update_log)
        self.signals.update_result.connect(self.update_result_table)
        self.signals.error.connect(self.show_error)

        # 添加初始日志
        self.update_log("股票数据验证工具已启动，请输入股票代码后点击'验证数据'按钮")

    def update_log(self, message):
        """更新日志显示"""
        current_time = time.strftime('%H:%M:%S')
        log_message = f"[{current_time}] {message}"
        self.log_text.append(log_message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum())

    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, '错误', message)

    def update_result_table(self, data):
        """更新结果表格"""
        # 清空表格
        self.result_table.setRowCount(0)

        # 添加数据行
        for i, (item, values) in enumerate(data.items()):
            self.result_table.insertRow(i)
            self.result_table.setItem(i, 0, QTableWidgetItem(item))

            # 设置各列数据
            for col, value in enumerate(values, start=1):
                if value is not None:
                    self.result_table.setItem(i, col, QTableWidgetItem(str(value)))
                else:
                    self.result_table.setItem(i, col, QTableWidgetItem("N/A"))

    def validate_stock_data(self):
        """验证股票数据"""
        stock_code = self.code_edit.text().strip()
        if not stock_code:
            self.show_error("请输入股票代码")
            return

        self.update_log(f"开始验证股票 {stock_code} 的数据")

        # 收集各数据源的数据
        result_data = {}

        # 从本地数据库获取数据
        db_data = self.get_db_data(stock_code) if self.db_checkbox.isChecked() else None

        # 从Tushare API获取数据
        tushare_data = self.get_tushare_data(stock_code) if self.tushare_checkbox.isChecked() else None

        # 不再计算通达信估算数据
        tdx_data = None

        # 整理数据
        self.organize_data(result_data, db_data, tushare_data, tdx_data)

        # 更新结果表格
        self.signals.update_result.emit(result_data)

        self.update_log(f"股票 {stock_code} 的数据验证完成")

    def get_db_data(self, stock_code):
        """从本地数据库获取股票数据"""
        try:
            db_path = self.db_path_edit.text().strip()
            table_name = self.table_edit.text().strip()

            self.update_log(f"正在从数据库 {db_path} 的表 {table_name} 中获取股票 {stock_code} 的数据")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 尝试多种可能的代码格式
            possible_codes = [stock_code]  # 原始代码

            # 如果是纯数字代码（如600001）
            if stock_code.isdigit():
                if stock_code.startswith('6'):
                    possible_codes.append(f"SH{stock_code}")  # 添加SH前缀
                    possible_codes.append(f"{stock_code}.SH")  # 添加.SH后缀
                else:
                    possible_codes.append(f"SZ{stock_code}")  # 添加SZ前缀
                    possible_codes.append(f"{stock_code}.SZ")  # 添加.SZ后缀

            # 如果是带前缀的代码（如SH600001）
            elif (stock_code.upper().startswith('SH') or stock_code.upper().startswith('SZ')) and stock_code[2:].isdigit():
                prefix = stock_code[:2].upper()
                number_part = stock_code[2:]
                possible_codes.append(number_part)  # 纯数字部分
                possible_codes.append(f"{number_part}.{prefix}")  # 带后缀格式 (600001.SH)

            # 如果是带后缀的代码（如600001.SH）
            elif '.' in stock_code:
                parts = stock_code.split('.')
                number_part = parts[0]
                suffix = parts[1].upper()
                possible_codes.append(number_part)  # 纯数字部分
                possible_codes.append(f"{suffix}{number_part}")  # 带前缀格式 (SH600001)

            # 构建查询条件
            placeholders = ', '.join(['?'] * len(possible_codes))
            self.update_log(f"尝试查询的代码格式: {', '.join(possible_codes)}")

            # 查询股票数据
            cursor.execute(f"SELECT 代码, 名称, 股价, 流通股, 市值Z FROM [{table_name}] WHERE 代码 IN ({placeholders})", possible_codes)
            result = cursor.fetchone()

            conn.close()

            if result:
                code, name, price, free_share, market_cap = result
                self.update_log(f"从数据库获取到股票 {code} ({name}) 的数据")
                return {
                    "代码": code,
                    "名称": name,
                    "股价": price,
                    "流通股": free_share,
                    "市值": market_cap
                }
            else:
                self.update_log(f"数据库中未找到股票 {stock_code} 的数据")
                return None
        except Exception as e:
            self.update_log(f"从数据库获取数据时出错: {str(e)}")
            return None

    def get_tushare_data(self, stock_code):
        """从Tushare API获取股票数据"""
        try:
            if not self.pro:
                self.update_log("Tushare API未初始化，请检查token是否有效")
                self.update_log(f"当前token: {self.token}")
                # 尝试重新初始化
                if self.token:
                    self.update_log("尝试重新初始化Tushare API...")
                    ts.set_token(self.token)
                    self.pro = ts.pro_api()
                    if not self.pro:
                        self.update_log("重新初始化Tushare API失败")
                        return None
                else:
                    return None

            self.update_log(f"正在从Tushare API获取股票 {stock_code} 的数据")

            # 转换为Tushare格式的代码
            ts_code = self._convert_to_ts_code(stock_code)
            if not ts_code:
                self.update_log(f"无法将 {stock_code} 转换为Tushare格式的代码")
                return None

            self.update_log(f"转换后的Tushare代码: {ts_code}")

            # 获取股票基本信息
            try:
                stock_info = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,name,list_date')
                if stock_info.empty:
                    self.update_log(f"未找到股票 {ts_code} 的基本信息，尝试使用模糊查询...")
                    # 尝试使用模糊查询
                    if ts_code.endswith('.SH') or ts_code.endswith('.SZ'):
                        code_num = ts_code.split('.')[0]
                        all_stocks = self.pro.stock_basic(fields='ts_code,name,list_date')
                        stock_info = all_stocks[all_stocks['ts_code'].str.startswith(code_num)]
                        if stock_info.empty:
                            self.update_log(f"模糊查询也未找到股票 {ts_code} 的基本信息")
                            return None
            except Exception as e:
                self.update_log(f"获取股票基本信息时出错: {str(e)}")
                return None

            name = stock_info.iloc[0]['name']
            ts_code = stock_info.iloc[0]['ts_code']  # 使用查询到的准确代码
            self.update_log(f"获取到股票基本信息: {ts_code} - {name}")

            # 获取最新交易日期
            try:
                import datetime
                today = datetime.datetime.now().strftime('%Y%m%d')
                yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y%m%d')

                # 先尝试获取今天的数据
                df_daily = self.pro.daily(ts_code=ts_code, trade_date=today)

                # 如果今天没有数据，尝试获取昨天的数据
                if df_daily.empty:
                    self.update_log(f"未找到股票 {ts_code} 在 {today} 的行情数据，尝试获取昨天的数据...")
                    df_daily = self.pro.daily(ts_code=ts_code, trade_date=yesterday)

                # 如果昨天也没有数据，尝试获取最近的交易日数据
                if df_daily.empty:
                    self.update_log(f"未找到股票 {ts_code} 在 {yesterday} 的行情数据，尝试获取最近的交易日数据...")
                    # 获取最近10个交易日
                    trade_cal = self.pro.trade_cal(exchange='SSE', is_open=1, start_date=(datetime.datetime.now() - datetime.timedelta(days=20)).strftime('%Y%m%d'), end_date=today)
                    if not trade_cal.empty:
                        # 按日期降序排序
                        trade_cal = trade_cal.sort_values(by='cal_date', ascending=False)
                        # 遍历尝试获取数据
                        for _, row in trade_cal.iterrows():
                            latest_trade_date = row['cal_date']
                            self.update_log(f"尝试获取 {latest_trade_date} 的行情数据...")
                            df_daily = self.pro.daily(ts_code=ts_code, trade_date=latest_trade_date)
                            if not df_daily.empty:
                                self.update_log(f"成功获取到 {latest_trade_date} 的行情数据")
                                break
            except Exception as e:
                self.update_log(f"获取行情数据时出错: {str(e)}")
                return None

            if df_daily.empty:
                self.update_log(f"未能找到股票 {ts_code} 的任何行情数据")
                return None

            price = round(float(df_daily.iloc[0]['close']), 2)
            trade_date = df_daily.iloc[0]['trade_date'] if 'trade_date' in df_daily.columns else '未知'
            self.update_log(f"获取到股票 {ts_code} 在 {trade_date} 的收盘价: {price}")

            # 获取自由流通股数据
            try:
                df_basic = self.pro.daily_basic(ts_code=ts_code, trade_date=trade_date, fields='ts_code,free_share,circ_mv')
                if df_basic.empty:
                    self.update_log(f"未找到股票 {ts_code} 的基本面数据，尝试获取其他数据源...")
                    # 尝试获取股本数据
                    df_share = self.pro.bak_basic(ts_code=ts_code, fields='ts_code,float_share')
                    if not df_share.empty:
                        free_share = round(float(df_share.iloc[0]['float_share']), 2)  # 单位：亿股
                        market_cap = round(free_share * price, 2)  # 估算市值
                    else:
                        self.update_log(f"未找到股票 {ts_code} 的股本数据，使用默认值")
                        free_share = 0
                        market_cap = 0
                else:
                    free_share = round(float(df_basic.iloc[0]['free_share']) / 10000, 2)  # 转换为亿股
                    market_cap = round(float(df_basic.iloc[0]['circ_mv']) / 10000, 2)  # 转换为亿元
            except Exception as e:
                self.update_log(f"获取基本面数据时出错: {str(e)}")
                free_share = 0
                market_cap = 0

            self.update_log(f"从Tushare获取到股票 {stock_code} ({name}) 的数据: 股价={price}, 流通股={free_share}, 市值={market_cap}")
            return {
                "代码": stock_code,
                "名称": name,
                "股价": str(price),  # 转为字符串，与数据库保持一致
                "流通股": free_share,
                "市值": market_cap
            }
        except Exception as e:
            self.update_log(f"从Tushare获取数据时出错: {str(e)}")
            import traceback
            self.update_log(f"错误详情: {traceback.format_exc()}")
            return None

    def calculate_tdx_data(self, stock_code):
        """计算通达信估算的数据"""
        try:
            self.update_log(f"正在计算股票 {stock_code} 的通达信估算数据")

            # 从Tushare获取基础数据
            if not self.pro:
                self.update_log("Tushare API未初始化，无法计算通达信估算数据")
                # 尝试重新初始化
                if self.token:
                    self.update_log("尝试重新初始化Tushare API...")
                    ts.set_token(self.token)
                    self.pro = ts.pro_api()
                    if not self.pro:
                        self.update_log("重新初始化Tushare API失败")
                        return None
                else:
                    return None

            # 转换为Tushare格式的代码
            ts_code = self._convert_to_ts_code(stock_code)
            if not ts_code:
                self.update_log(f"无法将 {stock_code} 转换为Tushare格式的代码")
                return None

            self.update_log(f"转换后的Tushare代码: {ts_code}")

            # 获取股票基本信息
            try:
                stock_info = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,name,list_date')
                if stock_info.empty:
                    self.update_log(f"未找到股票 {ts_code} 的基本信息，尝试使用模糊查询...")
                    # 尝试使用模糊查询
                    if ts_code.endswith('.SH') or ts_code.endswith('.SZ'):
                        code_num = ts_code.split('.')[0]
                        all_stocks = self.pro.stock_basic(fields='ts_code,name,list_date')
                        stock_info = all_stocks[all_stocks['ts_code'].str.startswith(code_num)]
                        if stock_info.empty:
                            self.update_log(f"模糊查询也未找到股票 {ts_code} 的基本信息")
                            return None
            except Exception as e:
                self.update_log(f"获取股票基本信息时出错: {str(e)}")
                return None

            name = stock_info.iloc[0]['name']
            ts_code = stock_info.iloc[0]['ts_code']  # 使用查询到的准确代码
            self.update_log(f"获取到股票基本信息: {ts_code} - {name}")

            # 获取最新交易日期和行情数据
            try:
                import datetime
                today = datetime.datetime.now().strftime('%Y%m%d')
                yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y%m%d')

                # 先尝试获取今天的数据
                df_daily = self.pro.daily(ts_code=ts_code, trade_date=today)

                # 如果今天没有数据，尝试获取昨天的数据
                if df_daily.empty:
                    self.update_log(f"未找到股票 {ts_code} 在 {today} 的行情数据，尝试获取昨天的数据...")
                    df_daily = self.pro.daily(ts_code=ts_code, trade_date=yesterday)

                # 如果昨天也没有数据，尝试获取最近的交易日数据
                if df_daily.empty:
                    self.update_log(f"未找到股票 {ts_code} 在 {yesterday} 的行情数据，尝试获取最近的交易日数据...")
                    # 获取最近10个交易日
                    trade_cal = self.pro.trade_cal(exchange='SSE', is_open=1, start_date=(datetime.datetime.now() - datetime.timedelta(days=20)).strftime('%Y%m%d'), end_date=today)
                    if not trade_cal.empty:
                        # 按日期降序排序
                        trade_cal = trade_cal.sort_values(by='cal_date', ascending=False)
                        # 遍历尝试获取数据
                        for _, row in trade_cal.iterrows():
                            latest_trade_date = row['cal_date']
                            self.update_log(f"尝试获取 {latest_trade_date} 的行情数据...")
                            df_daily = self.pro.daily(ts_code=ts_code, trade_date=latest_trade_date)
                            if not df_daily.empty:
                                self.update_log(f"成功获取到 {latest_trade_date} 的行情数据")
                                break
            except Exception as e:
                self.update_log(f"获取行情数据时出错: {str(e)}")
                return None

            if df_daily.empty:
                self.update_log(f"未能找到股票 {ts_code} 的任何行情数据")
                return None

            price = round(float(df_daily.iloc[0]['close']), 2)
            trade_date = df_daily.iloc[0]['trade_date'] if 'trade_date' in df_daily.columns else '未知'
            self.update_log(f"获取到股票 {ts_code} 在 {trade_date} 的收盘价: {price}")

            # 获取总流通股数据（对应通达信的FINANCE(46)）
            try:
                df_share = self.pro.bak_basic(ts_code=ts_code, fields='ts_code,float_share')
                if df_share.empty:
                    self.update_log(f"未找到股票 {ts_code} 的流通股数据，尝试其他方式获取...")
                    # 尝试从daily_basic获取
                    df_basic = self.pro.daily_basic(ts_code=ts_code, trade_date=trade_date, fields='ts_code,float_share')
                    if not df_basic.empty and 'float_share' in df_basic.columns:
                        total_float_share = round(float(df_basic.iloc[0]['float_share']) / 10000, 2)  # 转换为亿股
                    else:
                        self.update_log(f"无法获取股票 {ts_code} 的流通股数据，使用默认值")
                        total_float_share = 0
                else:
                    total_float_share = round(float(df_share.iloc[0]['float_share']), 2)  # 单位：亿股
            except Exception as e:
                self.update_log(f"获取流通股数据时出错: {str(e)}")
                total_float_share = 0

            # 计算通达信公式: 流通市值 := FINANCE(46) * CLOSE / 100000000
            # 由于total_float_share已经是亿股单位，所以直接乘以price
            tdx_market_cap = round(total_float_share * price, 2)

            self.update_log(f"计算得到股票 {stock_code} 的通达信估算数据: 股价={price}, 流通股={total_float_share}, 市值={tdx_market_cap}")
            return {
                "代码": stock_code,
                "名称": name,
                "股价": str(price),  # 转为字符串，与数据库保持一致
                "流通股": total_float_share,
                "市值": tdx_market_cap
            }
        except Exception as e:
            self.update_log(f"计算通达信估算数据时出错: {str(e)}")
            import traceback
            self.update_log(f"错误详情: {traceback.format_exc()}")
            return None

    def _convert_to_ts_code(self, stock_code):
        """将普通股票代码转换为Tushare格式的代码"""
        # 如果已经是Tushare格式（如600001.SH）
        if '.' in stock_code:
            return stock_code.upper()

        # 如果是纯数字代码
        if stock_code.isdigit():
            if stock_code.startswith('6'):
                return f"{stock_code}.SH"
            else:
                return f"{stock_code}.SZ"

        # 如果是带前缀的代码（如SH600001）
        if stock_code.upper().startswith('SH') or stock_code.upper().startswith('SZ'):
            prefix = stock_code[:2].upper()
            number_part = stock_code[2:]
            return f"{number_part}.{prefix}"

        return None

    def organize_data(self, result_data, db_data, tushare_data, tdx_data):
        """整理各数据源的数据，计算差异"""
        # 定义要显示的数据项
        data_items = ["名称", "股价", "流通股", "市值"]

        for item in data_items:
            db_value = db_data.get(item) if db_data else None
            tushare_value = tushare_data.get(item) if tushare_data else None
            # 不再使用通达信估算数据

            # 计算差异说明
            diff = self.calculate_difference(item, db_value, tushare_value, None)

            # 添加到结果数据中 (移除通达信列)
            result_data[item] = [db_value, tushare_value, diff]

    def calculate_difference(self, item, db_value, tushare_value, tdx_value):
        """计算数据差异并生成说明"""
        if item == "名称":
            # 名称只需比较是否相同
            if db_value and tushare_value and db_value != tushare_value:
                return f"数据库与Tushare不一致"
            return "一致"

        # 对于数值型数据，计算差异百分比
        values = []
        if db_value is not None:
            try:
                values.append(float(db_value))
            except:
                pass

        if tushare_value is not None:
            try:
                values.append(float(tushare_value))
            except:
                pass

        # 不再使用通达信估算数据

        if len(values) < 2:
            return "数据不足以比较"

        # 计算最大差异百分比
        min_val = min(values)
        max_val = max(values)

        if min_val == 0:
            return "差异过大"

        diff_pct = (max_val - min_val) / min_val * 100

        if diff_pct < 1:
            return "差异小于1%"
        elif diff_pct < 5:
            return f"差异 {diff_pct:.2f}%"
        elif diff_pct < 10:
            return f"差异较大 {diff_pct:.2f}%"
        else:
            return f"差异显著 {diff_pct:.2f}%"
