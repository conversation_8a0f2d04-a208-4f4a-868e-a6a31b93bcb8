# ========== 主配置文件 config.ini ==========
# 说明：本文件用于手动设置界面、控件、显示等参数，程序只读取不写入，注释永远不会丢失。
# 想让控件更大/更小、字体更粗/更细、间距更宽/更窄，直接用记事本或VSCode修改本文件，保存后重启程序即可生效。

[DEFAULT]
# columns: 显示的列名及字段映射（格式：界面名:字段名, ...）
# 说明：此参数用于配置界面显示的列名和对应的字段名，格式为"界面名:字段名"，多个列名用逗号分隔。
# 建议：根据自己的需求添加或删除列名，注意保持格式正确。
columns = 涨幅:pct_chg,代码:ts_code,名称:name,申万行业:申万行业,通达信行业:通达信行业,可比行业:可比行业,股价:close,50天:last50_up_count,18天:last18_up_count,12天:last12_up_count,连板:max_streak,历史:history_max_streak,市值Z:市值Z,流通股:float_share,涨停:limit_times,上市日期:list_date,城市:city,省份:province,同花顺标签:同花顺标签,开盘啦板块:开盘啦板块

# column_widths: 各列宽度（格式：列名:宽度, ...）
# 说明：此参数用于配置各列的宽度，格式为"列名:宽度"，多个列名用逗号分隔。
# 建议：根据自己的需求调整列宽，注意保持格式正确。
column_widths = 涨幅:112,代码:117,名称:125,申万行业:486,通达信行业:200,可比行业:206,股价:92,50天:93,18天:93,12天:93,连板:80,历史:80,市值Z:110,流通股:110,涨停:70,上市日期:138,城市:89,省份:89,同花顺标签:350,开盘啦板块:250

# numeric_columns: 需要右对齐的数字列
# 说明：此参数用于配置需要右对齐的数字列，多个列名用逗号分隔。
# 建议：根据自己的需求添加或删除列名，注意保持格式正确。
numeric_columns = 股价,市值Z,流通股,涨幅,50天,18天,12天,连板,历史

# left_aligned_columns: 左对齐的列名
# 说明：此参数用于配置左对齐的列名，多个列名用逗号分隔。
# 建议：根据自己的需求添加或删除列名，注意保持格式正确。
left_aligned_columns = 名称,申万行业,通达信行业,可比行业,城市,省份,同花顺标签,开盘啦板块

# right_aligned_columns: 右对齐的列名
# 说明：此参数用于配置右对齐的列名，多个列名用逗号分隔。
# 建议：根据自己的需求添加或删除列名，注意保持格式正确。
right_aligned_columns = 涨幅,代码,股价,市值Z,流通股,50天,18天,12天,连板,历史,上市日期

# 涨停统计天数设置
n1_days = 120
n2_days = 50
n3_days = 18

# header_height: 表头高度（像素），用于调整表头点击区域的高度
# 说明：此参数用于配置表头高度，建议取值为60。
header_height = 40

[API]
# token: Tushare API令牌
# 说明：此参数用于配置Tushare API令牌，请勿泄露，必填。
token = 284b804f2f919ea85cb7e6dfe617ff81f123c80b4cd3c4b13b35d736

[Tushare]
# token: Tushare API令牌
# 说明：此参数用于配置Tushare API令牌，请勿泄露，必填。
token = 284b804f2f919ea85cb7e6dfe617ff81f123c80b4cd3c4b13b35d736

[Settings]
# batch_size: tushare接口每批次请求数量
# 说明：此参数用于配置tushare接口每批次请求数量，建议取值为1000。
batch_size = 1000
# 数据更新的时间间隔（秒）
update_interval = 1
# threads: 多线程数量
# 说明：此参数用于配置多线程数量，建议取值为8。
threads = 6
# 默认查询的交易天数（已注释，因为每个查询都有指定的日期）
# trading_days = 10
# 最大显示行数
max_display_rows = 200
# go_bj_default_checked: 去北交勾选，默认不勾选
exclude_bj = false
# enable_linkage: 是否启用通达信联动功能，默认关闭
enable_linkage = true
# 涨停查询设置已移除，现在始终显示涨停数据
# cache_delay_seconds: 启动后保存缓存的延迟时间（秒）
# 说明：此参数用于配置程序启动后多少秒保存当前行情数据为缓存，用于加速重置操作
cache_delay_seconds = 20
# data_source_priority: 行情数据源优先级（tushare, tencent, sina）
# 说明：此参数用于配置行情数据源的优先级，可选值为tushare、tencent、sina，默认为sina。
data_source_priority = sina
# update_concept_stocks: 是否更新同花顺标签和开盘啦板块数据
# 说明：此参数用于配置是否在程序启动时更新同花顺标签和开盘啦板块数据，true表示更新，false表示不更新
update_concept_stocks = true

[TradingTime]
morning_start = 09:15
morning_end = 11:30
afternoon_start = 13:00
afternoon_end = 15:00

[Window]
# width: 主窗口宽度
# 说明：此参数用于配置主窗口宽度，建议取值为1500。
width = 1500
# height: 主窗口高度
# 说明：此参数用于配置主窗口高度，建议取值为900。
height = 800
# x_offset: 主窗口左上角X偏移
# 说明：此参数用于配置主窗口左上角X偏移，建议取值为10。
x_offset = 10
# y_offset: 主窗口左上角Y偏移
# 说明：此参数用于配置主窗口左上角Y偏移，建议取值为10。
y_offset = 10

[UI]
# 主题设置
theme = vista
# 背景颜色
background_color = #f5f5f5
# 文本颜色
text_color = #000000
# 边框颜色
border_color = #cccccc
# 字体设置
font_family = Microsoft YaHei
font_size = 10
# 表格行高
row_height = 25

[QueryPanel]
# input_width: 可输入框（代码、名称、主营业务）宽度（字符数，建议16~30）
# 说明：此参数用于配置可输入框的宽度，建议取值为16~30。
input_width = 14
# input_height: 可输入框高度（像素）
# 说明：此参数用于配置可输入框的高度，建议取值为16。
input_height = 16
# combobox_width: 下拉框（概念、叠加、申万行业、三级、二级、省份）宽度（字符数，建议16~30）
# 说明：此参数用于配置下拉框的宽度，建议取值为16~30。
combobox_width = 14

# kpl_concept_width: 开盘啦概念下拉框宽度（字符数，建议30~50）
# 说明：此参数用于配置开盘啦概念下拉框的宽度，由于概念名称可能较长，建议取值为30~50。
kpl_concept_width = 16
# combobox_height: 下拉框高度（像素）
# 说明：此参数用于配置下拉框的高度，建议取值为16。
combobox_height = 16
# font_size: 查询区所有控件字体大小
# 说明：此参数用于配置查询区所有控件的字体大小，建议取值为10。
font_size = 10
# row_spacing: 查询区每一行控件之间的垂直间隔高度（像素）
# 说明：此参数用于配置查询区每一行控件之间的垂直间隔高度，建议取值为8。
row_spacing = 8
# col_spacing: 查询区每个控件之间的水平间隔宽度（像素）
# 说明：此参数用于配置查询区每个控件之间的水平间隔宽度，建议取值为18。
col_spacing = 8

[Table]
# row_height: 表格每一行的高度（像素），建议28~40，行高越大内容越不挤压
row_height = 35
# font_size: 表格字体大小（像素），建议10~14
font_size = 10

[Path]
# ths_path: 同花顺安装路径
# 说明：此参数用于配置同花顺安装路径，请填写正确的路径。
ths_path = D:\股票\羽扇纶巾24082920

# tdx_window_title: 通达信窗口标题
# 说明：此参数用于配置通达信窗口标题，如果设置了此参数，将优先使用此标题查找窗口。
# 如果不设置，将通过窗口标题中包含"通达信"或"行情"来查找窗口。
# tdx_window_title = 通达信 - 行情分析

[Tencent]
# 腾讯行情批量获取参数
# tencent_batch_size: 每批请求股票数，建议40
# tencent_interval: 每批请求间隔秒数，支持抖动，如0.3表示0.3秒，抖动范围±0.05秒
# tencent_threads: 多线程数量，建议12
# tencent_interval_jitter: 每批请求间隔抖动范围
# tencent_preheat_interval: 盘前预热时的最低间隔（秒），如2.0
# tencent_open_time: 正式开盘时间，格式HH:MM
# tencent_preheat_minutes: 盘前预热分钟数
# tencent_error_pause: 触发403/Forbidden时暂停秒数，建议300（5分钟）
tencent_batch_size = 40
tencent_interval = 0.3
tencent_threads = 12
tencent_interval_jitter = 0.05
tencent_preheat_interval = 2.0
tencent_open_time = 09:30
tencent_preheat_minutes = 5
tencent_error_pause = 300

[Sina]
# 新浪行情批量获取参数
# sina_batch_size: 每批请求股票数，建议40
# sina_interval: 每批请求间隔秒数，支持抖动，如0.3表示0.3秒，抖动范围±0.05秒
# sina_threads: 多线程数量，建议12
# sina_preheat_interval: 盘前预热时的最低间隔（秒），如2.0
# sina_open_time: 正式开盘时间，格式HH:MM
# sina_preheat_minutes: 盘前预热分钟数
# sina_error_pause: 触发403/Forbidden时暂停秒数，建议300（5分钟）
sina_batch_size = 40
sina_interval = 0.2
sina_interval_jitter = 0.05
sina_threads = 12
sina_preheat_interval = 2.0
sina_open_time = 09:30
sina_preheat_minutes = 5
sina_error_pause = 300
