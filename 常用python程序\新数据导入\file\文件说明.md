# 数据处理工具文件说明

## 主要文件及功能

| 文件名 | 对应标签页 | 功能描述 |
|--------|------------|----------|
| pawencai_gui_new.py | 主程序 | 集成了所有功能模块的GUI界面，是程序的入口点 |
| db_importer.py | 数据库导入 | 提供数据库导入功能，用于导入各种数据源到数据库 |
| stock_updater.py | 股票数据更新 | 用于更新股票基础数据和行情数据 |
| stock_validator.py | 股票数据验证 | 验证股票数据的完整性和准确性 |
| db_debugger.py | 数据库调试 | 提供数据库查询和调试功能，可执行SQL语句 |
| ths_concept_updater_qt_fixed.py | 同花顺 | 更新同花顺概念和成分股数据 |
| kpl_data_qt.py | 开盘啦 | 管理开盘啦相关的数据 |
| db_utils.py | 通用工具 | 提供数据库连接和操作的通用函数 |
| package_tool.py | 打包为EXE | 提供将Python程序打包为可执行文件的功能 |
| comparable_companies.py | 可比公司 | 爬取股票行业信息并更新数据库中的可比行业字段 |

## 程序启动方法

1. 运行 `pawencai_gui_new.py` 启动主程序
2. 通过标签页切换不同功能模块

## 功能说明

### 数据库导入
- 支持从CSV、Excel等格式导入数据

### 可比公司
- 支持按照上市日期范围爬取股票行业信息（默认最近100天上市的股票）
- 将爬取的数据导入到数据库，只更新"可比行业"字段，不影响其他字段
- 支持导出Excel文件，并可以直接打开文件所在文件夹
- 使用多线程处理爬取和导入任务，避免界面卡顿
- 可配置导入参数和映射关系

### 股票数据更新
- 支持更新股票基本信息
- 支持更新股票行情数据
- 支持增量更新和全量更新

### 股票数据验证
- 检查数据完整性
- 验证数据一致性
- 提供错误修复建议

### 数据库调试
- 执行自定义SQL查询
- 查看表结构
- 查看查询结果
- 记录操作日志

### 同花顺概念更新
- 更新同花顺概念列表
- 更新概念成分股
- 测试随机概念股票

### 开盘啦数据管理
- 管理开盘啦相关数据
- 提供数据查询和分析功能

### 打包为EXE
- 支持将Python程序打包为独立可执行文件
- 提供单文件模式和目录模式选项
- 支持自定义图标、隐藏导入和数据文件
- 支持使用spec文件进行高级配置
- 配置保存在config.ini文件中的[Package]部分

## 打包功能使用说明

### 基本打包步骤
1. 点击“打包为EXE”标签页
2. 设置主脚本文件路径、输出目录和Python解释器路径
3. 选择打包选项（单文件模式、显示控制台等）
4. 点击“开始打包”按钮开始打包过程

### 使用spec文件
1. 如果选择“使用spec文件”，将使用指定的spec文件进行打包
2. spec文件允许更精细的控制打包过程和结果

### 高级选项
- 隐藏导入：指定需要包含的模块，用逗号分隔（如pandas,numpy,tkinter）
- 数据文件：指定需要包含的数据文件
- 额外参数：指定传递给PyInstaller的其他命令行参数

### 注意事项
- 打包过程可能需要几分钟时间，取决于程序的复杂度和依赖关系
- 打包完成后的文件将保存在指定的输出目录中
- 如果打包过程中出现错误，请查看日志区域的错误信息

## 注意事项

1. 所有文件都放在同一目录下，确保相互引用正常
2. 程序依赖Python 3.x和相关库
3. 数据库文件默认为stock.db，位于程序目录
4. 使用同花顺功能需要有效的Tushare API Token
