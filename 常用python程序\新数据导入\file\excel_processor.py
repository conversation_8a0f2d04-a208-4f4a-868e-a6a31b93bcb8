import pandas as pd
import numpy as np
import os
import re
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFileDialog, QTableWidget, QTableWidgetItem, QComboBox,
                            QLineEdit, QMessageBox, QHeaderView, QCheckBox, QGroupBox,
                            QListWidget, QListWidgetItem, QDialog, QFormLayout,
                            QProgressBar, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal

class ExcelProcessor:
    """Excel处理类，用于处理爬取的股票数据"""

    @staticmethod
    def load_excel(file_path):
        """加载Excel文件"""
        try:
            df = pd.read_excel(file_path)

            # 删除包含"数据来源于：i问财网站（iwencai.com）"的行
            if not df.empty:
                # 检查每一行是否包含该文本
                rows_to_drop = []
                for i, row in df.iterrows():
                    for cell in row:
                        if isinstance(cell, str) and "数据来源于：i问财网站" in cell:
                            rows_to_drop.append(i)
                            break

                if rows_to_drop:
                    print(f"删除包含'数据来源于：i问财网站'的行: {rows_to_drop}")
                    df = df.drop(rows_to_drop)
                    df = df.reset_index(drop=True)  # 重置索引

            return df, None
        except Exception as e:
            return None, str(e)

    @staticmethod
    def keep_only_specified_columns(df):
        """只保留指定的列"""
        # 要保留的列关键词及其优先级顺序
        keywords_priority = [
            '涨跌幅', '涨幅', '涨跌(%)', '涨跌幅(%)', '涨跌幅(%)',
            '股票代码', '代码',
            '股票简称', '名称', '股票名称',
            '现价', '价格', '股价', '最新价',
            '自由流通市值', '市值',  # 注意：移除了'a股流通市值'
            '上市板块', '板块',
            '所属概念', '概念',  # 保留'所属概念'
            '自由流通股', '流通股',
            '上市日期',
            '城市',
            '省份',
            '所属申万行业', '申万行业',
            '所属同花顺行业', '同花顺行业'
        ]

        # 要排除的列关键词
        exclude_keywords = [
            '所属概念数量', '概念数量',
            'a股流通市值', 'A股流通市值'
        ]

        # 打印所有列名，用于调试
        print(f"原始列名: {list(df.columns)}")

        # 查找匹配的列
        columns_to_keep = []
        matched_keywords = set()  # 用于跟踪已匹配的关键词

        # 首先排除不需要的列
        columns_to_exclude = []
        for col in df.columns:
            if any(excl.lower() in str(col).lower() for excl in exclude_keywords):
                columns_to_exclude.append(col)
                print(f"排除列: {col}")

        # 首先尝试精确匹配
        for keyword in keywords_priority:
            if keyword in df.columns and keyword not in matched_keywords:
                if keyword not in columns_to_exclude:
                    columns_to_keep.append(keyword)
                    matched_keywords.add(keyword)
                    print(f"精确匹配保留列: {keyword}")

        # 然后尝试部分匹配
        for col in df.columns:
            if col not in columns_to_keep and col not in columns_to_exclude:  # 避免重复添加和排除的列
                for keyword in keywords_priority:
                    if keyword.lower() in str(col).lower() and keyword not in matched_keywords:
                        columns_to_keep.append(col)
                        matched_keywords.add(keyword)
                        print(f"部分匹配保留列: {col} (匹配关键词: {keyword})")
                        break

        # 如果没有找到任何匹配的列，返回原始DataFrame
        if not columns_to_keep:
            print("警告: 未找到任何匹配的列")
            return df, "未找到任何匹配的列"

        print(f"保留的列: {columns_to_keep}")

        # 只保留匹配的列
        df_filtered = df[columns_to_keep].copy()

        return df_filtered, None

    @staticmethod
    def split_and_add_columns(df):
        """拆分行业列并添加新列"""
        print("开始拆分行业列...")

        # 查找申万行业列
        sw_col = None
        for col in df.columns:
            if '申万行业' in str(col):
                sw_col = col
                print(f"找到申万行业列: {sw_col}")
                break

        # 查找同花顺行业列
        ths_col = None
        for col in df.columns:
            if '同花顺行业' in str(col):
                ths_col = col
                print(f"找到同花顺行业列: {ths_col}")
                break

        # 如果没有找到行业列，确保仍然添加所需的列
        if sw_col is None:
            print("未找到申万行业列，添加空列")
            df['所属申万行业'] = ''
            sw_col = '所属申万行业'

        if ths_col is None:
            print("未找到同花顺行业列，添加空列")
            df['所属同花顺行业'] = ''
            ths_col = '所属同花顺行业'

        # 拆分申万行业
        # 获取申万行业列的索引
        sw_index = list(df.columns).index(sw_col)

        # 创建新列
        df.insert(sw_index + 1, '申万一级', '')
        df.insert(sw_index + 2, '申万二级', '')
        df.insert(sw_index + 3, '申万三级', '')

        # 拆分数据
        for i, row in df.iterrows():
            if pd.notna(row[sw_col]) and isinstance(row[sw_col], str):
                # 使用--作为分隔符（根据需求修改）
                parts = row[sw_col].split('--')
                if len(parts) >= 1:
                    df.at[i, '申万一级'] = parts[0].strip()
                if len(parts) >= 2:
                    df.at[i, '申万二级'] = parts[1].strip()
                if len(parts) >= 3:
                    df.at[i, '申万三级'] = parts[2].strip()

        # 拆分同花顺行业
        # 获取同花顺行业列的索引
        ths_index = list(df.columns).index(ths_col)

        # 创建新列
        df.insert(ths_index + 1, '同花顺一级', '')
        df.insert(ths_index + 2, '同花顺二级', '')
        df.insert(ths_index + 3, '同花顺三级', '')

        # 拆分数据
        for i, row in df.iterrows():
            if pd.notna(row[ths_col]) and isinstance(row[ths_col], str):
                # 使用-作为分隔符（同花顺行业使用单横线分隔）
                parts = row[ths_col].split('-')
                if len(parts) >= 1:
                    df.at[i, '同花顺一级'] = parts[0].strip()
                if len(parts) >= 2:
                    df.at[i, '同花顺二级'] = parts[1].strip()
                if len(parts) >= 3:
                    df.at[i, '同花顺三级'] = parts[2].strip()

        # 在同花顺三级后添加三列
        ths3_index = list(df.columns).index('同花顺三级')
        df.insert(ths3_index + 1, '曾用名', '')
        df.insert(ths3_index + 2, '主营业务', '')
        df.insert(ths3_index + 3, '可比行业', '')

        print(f"拆分后的列: {list(df.columns)}")
        return df

    @staticmethod
    def rename_columns(df):
        """重命名列"""
        print("开始重命名列...")
        print(f"重命名前的列: {list(df.columns)}")

        # 列名映射字典 - 更全面的映射关系
        column_mapping = {
            '涨跌幅': '涨幅',
            '涨跌幅(%)': '涨幅',
            '涨跌(%)': '涨幅',
            '涨幅(%)': '涨幅',
            '股票代码': '代码',
            '代码': '代码',
            '股票简称': '名称',
            '名称': '名称',
            '股票名称': '名称',
            '现价': '股价',
            '现价(元)': '股价',
            '价格': '股价',
            '最新价': '股价',
            '最新价(元)': '股价',
            # 注意：根据需求，自由流通市值映射为市值Z，而不是a股流通市值
            '自由流通市值': '市值Z',
            '自由流通市值(元)': '市值Z',
            '上市板块': '板块',
            '板块': '板块',
            '所属概念': '概念',
            '概念': '概念',
            '自由流通股': '流通股',
            '自由流通股(股)': '流通股',
            '流通股': '流通股',
            '上市日期': '上市日期',
            '城市': '城市',
            '省份': '省份',
            '所属申万行业': '申万行业',
            '申万行业': '申万行业',
            '所属同花顺行业': '同花顺行业',
            '同花顺行业': '同花顺行业'
        }

        # 创建实际映射字典
        actual_mapping = {}
        for col in df.columns:
            # 精确匹配
            if col in column_mapping:
                actual_mapping[col] = column_mapping[col]
            else:
                # 部分匹配
                for old_name, new_name in column_mapping.items():
                    if old_name.lower() in str(col).lower():
                        actual_mapping[col] = new_name
                        break

        print(f"应用的列名映射: {actual_mapping}")

        # 应用映射
        df = df.rename(columns=actual_mapping)

        # 格式化概念列：将分号分隔的概念用【】括起来
        if '概念' in df.columns:
            print("格式化概念列...")
            for i, row in df.iterrows():
                if pd.notna(row['概念']) and isinstance(row['概念'], str):
                    concepts = row['概念'].split(';')
                    formatted_concepts = []
                    for concept in concepts:
                        concept = concept.strip()
                        if concept:  # 确保概念不为空
                            formatted_concepts.append(f"【{concept}】")
                    df.at[i, '概念'] = ';'.join(formatted_concepts)

        # 处理涨幅数据，保留原始数值
        if '涨幅' in df.columns:
            print("处理涨幅数据...")
            for i, row in df.iterrows():
                if pd.notna(row['涨幅']):
                    try:
                        # 尝试将值转换为数值
                        value = pd.to_numeric(row['涨幅'], errors='coerce')
                        if pd.notna(value):
                            # 保留原始数值，不做单位转换
                            df.at[i, '涨幅'] = value
                    except:
                        pass

        # 将市值Z和流通股转换为亿为单位，保留小数点后2位
        if '市值Z' in df.columns:
            print("将市值Z转换为亿为单位...")
            for i, row in df.iterrows():
                if pd.notna(row['市值Z']):
                    try:
                        # 尝试将值转换为数值
                        value = pd.to_numeric(row['市值Z'], errors='coerce')
                        if pd.notna(value):
                            # 转换为亿为单位，保留小数点后2位
                            df.at[i, '市值Z'] = round(value / 100000000, 2)
                    except:
                        pass

        if '流通股' in df.columns:
            print("将流通股转换为亿为单位...")
            for i, row in df.iterrows():
                if pd.notna(row['流通股']):
                    try:
                        # 尝试将值转换为数值
                        value = pd.to_numeric(row['流通股'], errors='coerce')
                        if pd.notna(value):
                            # 转换为亿为单位，保留小数点后2位
                            df.at[i, '流通股'] = round(value / 100000000, 2)
                    except:
                        pass

        print(f"重命名后的列: {list(df.columns)}")
        return df

    @staticmethod
    def reorder_columns(df):
        """重新排序列"""
        print("开始重新排序列...")
        print(f"排序前的列: {list(df.columns)}")

        # 目标列顺序 - 更新为与需求完全匹配的顺序
        target_order = [
            '涨幅', '代码', '名称', '股价', '市值Z', '板块', '概念', '流通股', '上市日期',
            '城市', '省份', '申万行业', '申万一级', '申万二级', '申万三级',
            '同花顺行业', '同花顺一级', '同花顺二级', '同花顺三级',
            '曾用名', '主营业务', '可比行业'
        ]

        # 获取实际存在的列
        existing_columns = []
        for col in target_order:
            if col in df.columns:
                existing_columns.append(col)
                print(f"找到目标列: {col}")
            else:
                print(f"未找到目标列: {col}")

        # 添加任何未在目标顺序中但存在于DataFrame中的列
        for col in df.columns:
            if col not in existing_columns:
                existing_columns.append(col)
                print(f"添加额外列: {col}")

        print(f"最终列顺序: {existing_columns}")

        # 重新排序
        df = df[existing_columns]

        return df

    @staticmethod
    def process_excel(file_path, progress_callback=None):
        """处理Excel文件的主函数"""
        try:
            # 加载Excel
            if progress_callback:
                progress_callback(10, "正在加载Excel文件...")
            print(f"\n\n开始处理Excel文件: {file_path}")
            df, error = ExcelProcessor.load_excel(file_path)
            if error:
                print(f"加载Excel失败: {error}")
                return None, error

            print(f"成功加载Excel，共 {len(df)} 行, {len(df.columns)} 列")

            # 只保留指定列
            if progress_callback:
                progress_callback(30, "正在筛选列...")
            df, error = ExcelProcessor.keep_only_specified_columns(df)
            if error:
                print(f"筛选列失败: {error}")
                return None, error

            print(f"筛选列后，保留 {len(df.columns)} 列")

            # 拆分并添加列
            if progress_callback:
                progress_callback(50, "正在拆分行业数据...")
            df = ExcelProcessor.split_and_add_columns(df)

            print(f"拆分行业后，共 {len(df.columns)} 列")

            # 重命名列
            if progress_callback:
                progress_callback(70, "正在重命名列...")
            df = ExcelProcessor.rename_columns(df)

            print(f"重命名列后，共 {len(df.columns)} 列")

            # 重新排序列
            if progress_callback:
                progress_callback(90, "正在重新排序列...")
            df = ExcelProcessor.reorder_columns(df)

            print(f"重新排序后，最终共 {len(df.columns)} 列")

            if progress_callback:
                progress_callback(100, "处理完成!")

            return df, None
        except Exception as e:
            import traceback
            print(f"处理Excel时出错: {str(e)}")
            print(traceback.format_exc())
            if progress_callback:
                progress_callback(0, f"处理失败: {str(e)}")
            return None, str(e)

class ExcelProcessorWidget(QWidget):
    """Excel处理界面，用于集成到主GUI"""

    def __init__(self):
        super().__init__()
        self.df = None
        self.processed_df = None
        self.initUI()

    def initUI(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建文件选择区域
        file_group = QGroupBox('数据文件')
        file_layout = QHBoxLayout()

        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText('选择要处理的Excel文件...')

        browse_button = QPushButton('浏览...')
        browse_button.clicked.connect(self.browse_file)

        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(browse_button)
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)

        # 创建操作按钮区域
        buttons_group = QGroupBox('操作')
        buttons_layout = QHBoxLayout()

        self.process_button = QPushButton('一键处理')
        self.process_button.clicked.connect(self.process_excel)
        self.process_button.setEnabled(False)

        self.save_button = QPushButton('保存结果')
        self.save_button.clicked.connect(self.save_excel)
        self.save_button.setEnabled(False)

        self.export_sw_button = QPushButton('导出申万行业')
        self.export_sw_button.clicked.connect(self.export_sw_industry)
        self.export_sw_button.setEnabled(False)

        buttons_layout.addWidget(self.process_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.export_sw_button)
        buttons_group.setLayout(buttons_layout)
        main_layout.addWidget(buttons_group)

        # 创建进度条
        progress_group = QGroupBox('处理进度')
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        self.status_label = QLabel('准备就绪')

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # 创建数据预览区域
        preview_group = QGroupBox('数据预览')
        preview_layout = QVBoxLayout()

        self.table_widget = QTableWidget()
        self.table_widget.setEditTriggers(QTableWidget.NoEditTriggers)

        preview_layout.addWidget(self.table_widget)
        preview_group.setLayout(preview_layout)
        main_layout.addWidget(preview_group)

        # 设置布局
        self.setLayout(main_layout)

    def browse_file(self):
        """浏览并选择Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择Excel文件', '', 'Excel文件 (*.xlsx *.xls)')
        if file_path:
            self.file_path_edit.setText(file_path)
            # 加载并预览数据
            self.load_and_preview(file_path)

    def load_and_preview(self, file_path):
        """加载并预览数据"""
        try:
            # 加载Excel文件
            self.df, error = ExcelProcessor.load_excel(file_path)
            if error:
                QMessageBox.critical(self, '错误', f'加载文件失败: {error}')
                return

            # 更新预览
            self.update_table_preview(self.df)

            # 启用处理按钮
            self.process_button.setEnabled(True)

            # 更新状态
            self.status_label.setText(f'已加载 {len(self.df)} 行数据')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'加载文件失败: {str(e)}')

    def update_table_preview(self, df, max_rows=100):
        """更新表格预览"""
        if df is None:
            return

        # 限制预览行数
        preview_df = df.head(max_rows)

        # 设置表格行列数
        self.table_widget.setRowCount(len(preview_df))
        self.table_widget.setColumnCount(len(preview_df.columns))

        # 设置表头
        self.table_widget.setHorizontalHeaderLabels(preview_df.columns.astype(str))

        # 填充数据
        for i, row in enumerate(preview_df.itertuples(index=False)):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value) if pd.notna(value) else '')
                self.table_widget.setItem(i, j, item)

        # 调整列宽
        self.table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)

        # 如果列太多，设置为可滚动
        if len(preview_df.columns) > 10:
            self.table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
            for i in range(len(preview_df.columns)):
                self.table_widget.setColumnWidth(i, 100)

    def update_progress(self, value, message):
        """更新进度条和状态消息"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def process_excel(self):
        """处理Excel文件"""
        if self.df is None:
            return

        try:
            # 处理Excel
            self.processed_df, error = ExcelProcessor.process_excel(
                self.file_path_edit.text(),
                progress_callback=self.update_progress
            )

            if error:
                QMessageBox.critical(self, '错误', f'处理文件失败: {error}')
                return

            # 更新预览
            self.update_table_preview(self.processed_df)

            # 启用保存按钮和导出申万行业按钮
            self.save_button.setEnabled(True)
            self.export_sw_button.setEnabled(True)

            # 更新状态
            self.status_label.setText(f'处理完成，共 {len(self.processed_df)} 行数据')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'处理文件失败: {str(e)}')

    def save_excel(self):
        """保存处理后的Excel文件"""
        if self.processed_df is None:
            return

        # 自动保存为db.xlsx，不弹出文件选择对话框
        file_path = 'db.xlsx'

        try:
            self.processed_df.to_excel(file_path, index=False)
            QMessageBox.information(self, '成功', f'文件已保存到: {file_path}')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存文件失败: {str(e)}')

    def export_sw_industry(self):
        """导出申万行业分类到文本文件"""
        if self.processed_df is None:
            QMessageBox.warning(self, '警告', '请先处理Excel文件')
            return

        # 获取保存路径
        input_file = self.file_path_edit.text()
        dir_name = os.path.dirname(input_file)
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        default_path = os.path.join(dir_name, f"{base_name}_申万行业.txt")

        file_path, _ = QFileDialog.getSaveFileName(
            self, '保存申万行业分类', default_path, '文本文件 (*.txt)')

        if not file_path:
            return

        try:
            # 更新状态
            self.status_label.setText('正在导出申万行业分类...')
            self.progress_bar.setValue(10)

            # 检查必要的列是否存在
            required_columns = ['代码', '板块']
            missing_columns = [col for col in required_columns if col not in self.processed_df.columns]

            if missing_columns:
                QMessageBox.warning(self, '警告', f'缺少必要的列: {", ".join(missing_columns)}')
                self.status_label.setText('导出失败: 缺少必要的列')
                return

            # 检查是否存在申万行业相关列
            sw_columns = ['申万行业', '申万一级', '申万二级', '申万三级']
            has_sw_column = any(col in self.processed_df.columns for col in sw_columns)

            if not has_sw_column:
                QMessageBox.warning(self, '警告', '缺少申万行业相关列，无法导出申万行业分类')
                self.status_label.setText('导出失败: 缺少申万行业相关列')
                return

            self.progress_bar.setValue(30)

            # 准备数据
            output_data = []
            total_rows = len(self.processed_df)

            for i, row in self.processed_df.iterrows():
                # 更新进度
                if i % 10 == 0:
                    progress = 30 + (i / total_rows) * 60
                    self.progress_bar.setValue(int(progress))
                    self.status_label.setText(f'正在处理第 {i+1}/{total_rows} 行...')

                # 获取股票代码
                code = str(row['代码']).strip()

                # 提取6位数字代码
                numeric_code = ''.join(filter(str.isdigit, code))[-6:].zfill(6)

                # 根据板块列确定市场代码
                board = str(row['板块']).strip()
                if '深' in board or '创业' in board or board == '主板' and code.startswith(('000', '001', '002', '003', '300', '301')):
                    market_code = '0'  # 深圳
                elif '沪' in board or '上海' in board or board == '主板' and code.startswith(('600', '601', '603', '605', '688', '689')):
                    market_code = '1'  # 上海
                elif '北' in board:
                    market_code = '2'  # 北交所
                else:
                    # 如果板块无法确定，则根据代码前缀判断
                    if code.startswith(('000', '001', '002', '003', '300', '301')):
                        market_code = '0'  # 深圳
                    elif code.startswith(('600', '601', '603', '605', '688', '689')):
                        market_code = '1'  # 上海
                    else:
                        market_code = '2'  # 北交所

                # 获取完整的申万行业分类
                if '申万行业' in self.processed_df.columns and pd.notna(row['申万行业']):
                    industry = str(row['申万行业'])
                else:
                    # 如果没有申万行业列，则尝试从申万一级、二级、三级拼接
                    industry_parts = []
                    if '申万一级' in self.processed_df.columns and pd.notna(row['申万一级']):
                        industry_parts.append(str(row['申万一级']))
                    if '申万二级' in self.processed_df.columns and pd.notna(row['申万二级']):
                        industry_parts.append(str(row['申万二级']))
                    if '申万三级' in self.processed_df.columns and pd.notna(row['申万三级']):
                        industry_parts.append(str(row['申万三级']))

                    industry = "--".join(industry_parts) if industry_parts else ""

                # 格式化为指定格式（使用|作为分隔符）
                output_line = f"{market_code}|{numeric_code}|{industry}|0.000"
                output_data.append(output_line)

            self.progress_bar.setValue(90)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                for line in output_data:
                    f.write(line + '\n')

            self.progress_bar.setValue(100)
            self.status_label.setText(f'申万行业分类已成功导出，共 {len(output_data)} 条记录')

            # 显示结果预览
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle('导出结果预览')
            preview_dialog.setMinimumSize(600, 400)

            layout = QVBoxLayout()

            # 添加预览标签
            label = QLabel(f'已成功导出 {len(output_data)} 条记录到: {file_path}')
            layout.addWidget(label)

            # 添加预览文本框
            preview_text = QTextEdit()
            preview_text.setReadOnly(True)

            # 只显示前20行
            preview_lines = output_data[:20]
            preview_content = '\n'.join(preview_lines)
            if len(output_data) > 20:
                preview_content += f'\n...\n(共 {len(output_data)} 行)'

            preview_text.setText(preview_content)
            layout.addWidget(preview_text)

            # 添加关闭按钮
            close_button = QPushButton('关闭')
            close_button.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_button)

            preview_dialog.setLayout(layout)
            preview_dialog.exec_()

        except Exception as e:
            self.progress_bar.setValue(0)
            self.status_label.setText(f'导出失败: {str(e)}')
            QMessageBox.critical(self, '错误', f'导出申万行业分类失败: {str(e)}')

# 测试代码
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    window = ExcelProcessorWidget()
    window.show()
    sys.exit(app.exec_())
