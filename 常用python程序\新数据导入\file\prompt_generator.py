import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import re
from datetime import datetime
import os
import sqlite3
from tkinter import filedialog
import yaml

class PromptGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("AI提示词生成器")
        self.root.geometry("1200x800")
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 先初始化数据库
        self.init_database()
        
        # 创建左侧面板（模板和历史）
        self.create_left_panel()
        
        # 创建中间面板（输入和输出）
        self.create_center_panel()
        
        # 创建右侧面板（分析和建议）
        self.create_right_panel()
        
        # 初始化关键词映射
        self.init_keyword_mapping()
        
        # 加载用户偏好
        self.load_user_preferences()
    
    def create_left_panel(self):
        # 左侧面板
        left_frame = ttk.LabelFrame(self.main_frame, text="模板和历史")
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 模板管理
        template_frame = ttk.LabelFrame(left_frame, text="模板管理")
        template_frame.pack(fill=tk.X, pady=5)
        
        # 模板分类
        self.template_category = tk.StringVar(value="开发需求")
        categories = ["开发需求", "数据分析", "UI设计", "其他"]
        ttk.Label(template_frame, text="分类:").pack(anchor=tk.W)
        for cat in categories:
            ttk.Radiobutton(template_frame, text=cat, variable=self.template_category, 
                           value=cat, command=self.load_templates).pack(anchor=tk.W)
        
        # 模板列表
        self.template_list = tk.Listbox(template_frame, height=5)
        self.template_list.pack(fill=tk.X, pady=5)
        self.template_list.bind('<<ListboxSelect>>', self.load_template)
        
        # 模板操作按钮
        template_btn_frame = ttk.Frame(template_frame)
        template_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(template_btn_frame, text="保存模板", command=self.save_template).pack(side=tk.LEFT, padx=2)
        ttk.Button(template_btn_frame, text="删除模板", command=self.delete_template).pack(side=tk.LEFT, padx=2)
        ttk.Button(template_btn_frame, text="导出模板", command=self.export_templates).pack(side=tk.LEFT, padx=2)
        ttk.Button(template_btn_frame, text="导入模板", command=self.import_templates).pack(side=tk.LEFT, padx=2)
        
        # 历史记录
        history_frame = ttk.LabelFrame(left_frame, text="历史记录")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 历史记录搜索
        search_frame = ttk.Frame(history_frame)
        search_frame.pack(fill=tk.X, pady=5)
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.history_search = ttk.Entry(search_frame)
        self.history_search.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.history_search.bind('<KeyRelease>', self.search_history)
        
        # 只看星标
        self.only_star_var = tk.BooleanVar(value=False)
        only_star_check = ttk.Checkbutton(search_frame, text="只看星标", variable=self.only_star_var, command=self.search_history)
        only_star_check.pack(side=tk.RIGHT)
        
        # 历史记录列表
        self.history_list = tk.Listbox(history_frame)
        self.history_list.pack(fill=tk.BOTH, expand=True, pady=5)
        self.history_list.bind('<<ListboxSelect>>', self.load_history)
        self.history_list.bind('<Double-Button-1>', self.toggle_star)
    
    def create_center_panel(self):
        # 中间面板
        center_frame = ttk.Frame(self.main_frame)
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 输入区域
        input_frame = ttk.LabelFrame(center_frame, text="自然语言需求输入")
        input_frame.pack(fill=tk.X, pady=5)
        
        self.input_text = scrolledtext.ScrolledText(input_frame, wrap=tk.WORD, height=10)
        self.input_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.input_text.bind('<KeyRelease>', self.auto_save_input)
        # 自动恢复上次输入内容
        self.restore_input()
        
        # 输出区域
        output_frame = ttk.LabelFrame(center_frame, text="结构化AI提示词")
        output_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.output_text = scrolledtext.ScrolledText(output_frame, wrap=tk.WORD, height=15)
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(center_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="生成提示词", command=self.generate_prompt).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="复制到剪贴板", command=self.copy_to_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出", command=self.export_prompt).pack(side=tk.LEFT, padx=5)
        
        # 格式选择
        format_frame = ttk.Frame(button_frame)
        format_frame.pack(side=tk.RIGHT)
        ttk.Label(format_frame, text="输出格式:").pack(side=tk.LEFT)
        self.output_format = tk.StringVar(value="Markdown")
        formats = ["文本", "Markdown", "JSON", "YAML"]
        format_menu = ttk.OptionMenu(format_frame, self.output_format, "Markdown", *formats, 
                                   command=self.change_output_format)
        format_menu.pack(side=tk.LEFT, padx=5)
    
    def create_right_panel(self):
        # 右侧面板
        right_frame = ttk.LabelFrame(self.main_frame, text="分析和建议")
        right_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 关键词分析
        keywords_frame = ttk.LabelFrame(right_frame, text="关键词分析")
        keywords_frame.pack(fill=tk.X, pady=5)
        
        self.keywords_text = scrolledtext.ScrolledText(keywords_frame, wrap=tk.WORD, height=5)
        self.keywords_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 优化建议
        suggestions_frame = ttk.LabelFrame(right_frame, text="优化建议")
        suggestions_frame.pack(fill=tk.X, pady=5)
        
        self.suggestions_text = scrolledtext.ScrolledText(suggestions_frame, wrap=tk.WORD, height=5)
        self.suggestions_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 质量评分
        score_frame = ttk.LabelFrame(right_frame, text="质量评分")
        score_frame.pack(fill=tk.X, pady=5)
        
        self.score_text = scrolledtext.ScrolledText(score_frame, wrap=tk.WORD, height=3)
        self.score_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def init_database(self):
        # 创建数据库连接
        self.conn = sqlite3.connect('prompt_generator.db')
        self.cursor = self.conn.cursor()
        
        # 创建模板表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT,
                name TEXT,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建历史记录表，增加star字段
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                input_text TEXT,
                output_text TEXT,
                category TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                star INTEGER DEFAULT 0
            )
        ''')
        
        # 创建用户偏好表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE,
                value TEXT
            )
        ''')
        
        self.conn.commit()
    
    def init_keyword_mapping(self):
        # 初始化关键词映射
        self.keyword_mapping = {
            "功能": ["功能", "需求", "要求", "需要"],
            "界面": ["界面", "UI", "显示", "展示"],
            "数据": ["数据", "信息", "内容"],
            "性能": ["性能", "速度", "效率", "响应"],
            "错误": ["错误", "异常", "问题", "bug"],
            "测试": ["测试", "验证", "检查"],
            "优化": ["优化", "改进", "提升", "完善"],
            "安全": ["安全", "保护", "权限"],
            "兼容": ["兼容", "适配", "支持"],
            "维护": ["维护", "更新", "升级"]
        }
    
    def load_user_preferences(self):
        # 加载用户偏好设置
        self.cursor.execute("SELECT key, value FROM preferences")
        preferences = self.cursor.fetchall()
        for key, value in preferences:
            if key == "last_category":
                self.template_category.set(value)
            elif key == "output_format":
                self.output_format.set(value)
    
    def save_user_preferences(self):
        # 保存用户偏好设置
        preferences = [
            ("last_category", self.template_category.get()),
            ("output_format", self.output_format.get())
        ]
        for key, value in preferences:
            self.cursor.execute("INSERT OR REPLACE INTO preferences (key, value) VALUES (?, ?)",
                              (key, value))
        self.conn.commit()
    
    def load_templates(self):
        # 加载当前分类的模板
        category = self.template_category.get()
        self.cursor.execute("SELECT name FROM templates WHERE category = ?", (category,))
        templates = self.cursor.fetchall()
        
        self.template_list.delete(0, tk.END)
        for template in templates:
            self.template_list.insert(tk.END, template[0])
    
    def load_template(self, event):
        # 加载选中的模板
        selection = self.template_list.curselection()
        if not selection:
            return
        
        template_name = self.template_list.get(selection[0])
        category = self.template_category.get()
        
        self.cursor.execute("SELECT content FROM templates WHERE name = ? AND category = ?",
                          (template_name, category))
        result = self.cursor.fetchone()
        
        if result:
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(tk.END, result[0])
    
    def save_template(self):
        # 保存当前内容为模板
        name = tk.simpledialog.askstring("保存模板", "请输入模板名称:")
        if not name:
            return
        
        content = self.input_text.get(1.0, tk.END).strip()
        category = self.template_category.get()
        
        self.cursor.execute("INSERT INTO templates (category, name, content) VALUES (?, ?, ?)",
                          (category, name, content))
        self.conn.commit()
        
        self.load_templates()
    
    def delete_template(self):
        # 删除选中的模板
        selection = self.template_list.curselection()
        if not selection:
            return
        
        template_name = self.template_list.get(selection[0])
        category = self.template_category.get()
        
        if messagebox.askyesno("确认删除", f"确定要删除模板 '{template_name}' 吗？"):
            self.cursor.execute("DELETE FROM templates WHERE name = ? AND category = ?",
                              (template_name, category))
            self.conn.commit()
            
            self.load_templates()
    
    def search_history(self, event=None):
        # 搜索历史记录
        search_text = self.history_search.get().strip()
        only_star = self.only_star_var.get()
        sql = "SELECT id, input_text, created_at, star FROM history WHERE (input_text LIKE ? OR output_text LIKE ?)"
        params = [f'%{search_text}%', f'%{search_text}%']
        if only_star:
            sql += " AND star = 1"
        sql += " ORDER BY created_at DESC"
        self.cursor.execute(sql, params)
        results = self.cursor.fetchall()
        self.history_list.delete(0, tk.END)
        for result in results:
            star = "★" if result[3] else "☆"
            self.history_list.insert(tk.END, f"{star} {result[2]} - {result[1][:50]}...")
    
    def load_history(self, event):
        # 加载选中的历史记录
        selection = self.history_list.curselection()
        if not selection:
            return
        history_text = self.history_list.get(selection[0])
        # 取时间戳部分
        created_at = history_text.split(" - ")[0][2:]
        self.cursor.execute("SELECT input_text, output_text FROM history WHERE created_at = ?",
                          (created_at,))
        result = self.cursor.fetchone()
        if result:
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(tk.END, result[0])
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, result[1])
    
    def generate_prompt(self):
        # 获取输入文本
        input_text = self.input_text.get(1.0, tk.END).strip()
        if not input_text:
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, "请输入需求描述")
            return
        # 批量处理：每行一条需求
        lines = [line.strip() for line in input_text.split('\n') if line.strip()]
        results = []
        for idx, line in enumerate(lines, 1):
            structured_prompt = self.analyze_text(line)
            if self.output_format.get() == "文本":
                results.append(f"【第{idx}条】\n" + structured_prompt + "\n" + ("-"*40))
            elif self.output_format.get() == "Markdown":
                results.append(f"## 第{idx}条\n" + structured_prompt + "\n---\n")
            elif self.output_format.get() == "JSON":
                results.append(json.loads(structured_prompt))
            elif self.output_format.get() == "YAML":
                results.append(structured_prompt)
        # 合并输出
        self.output_text.delete(1.0, tk.END)
        if self.output_format.get() == "JSON":
            self.output_text.insert(tk.END, json.dumps(results, ensure_ascii=False, indent=2))
        elif self.output_format.get() == "YAML":
            self.output_text.insert(tk.END, '\n---\n'.join(results))
        else:
            self.output_text.insert(tk.END, '\n'.join(results))
        # 保存到历史记录（只保存全部输入和全部输出）
        self.save_to_history(input_text, self.output_text.get(1.0, tk.END))
        # 更新分析和建议（只分析全部输入）
        self.update_analysis(input_text)
    
    def analyze_text(self, text):
        # 初始化结构化提示词
        structured = {
            "需求概述": "",
            "功能要求": [],
            "技术细节": [],
            "注意事项": [],
            "约束条件": []
        }
        
        # 分割段落
        paragraphs = text.split('\n')
        
        # 分析每个段落
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
            
            # 识别段落类型
            if "理解" in para or "说说" in para:
                structured["需求概述"] = para
            elif "注意" in para or "需要" in para:
                structured["注意事项"].append(para)
            elif "代码" in para or "实现" in para:
                structured["技术细节"].append(para)
            elif "功能" in para or "要求" in para:
                structured["功能要求"].append(para)
            else:
                structured["约束条件"].append(para)
        
        # 根据输出格式转换
        format_type = self.output_format.get()
        if format_type == "JSON":
            return json.dumps(structured, ensure_ascii=False, indent=2)
        elif format_type == "YAML":
            return yaml.dump(structured, allow_unicode=True)
        elif format_type == "Markdown":
            return self.convert_to_markdown(structured)
        else:
            return self.convert_to_text(structured)
    
    def convert_to_text(self, structured):
        result = "AI提示词结构：\n\n"
        
        if structured["需求概述"]:
            result += f"1. 需求概述：\n{structured['需求概述']}\n\n"
        
        if structured["功能要求"]:
            result += "2. 功能要求：\n"
            for i, req in enumerate(structured["功能要求"], 1):
                result += f"{i}. {req}\n"
            result += "\n"
        
        if structured["技术细节"]:
            result += "3. 技术细节：\n"
            for i, detail in enumerate(structured["技术细节"], 1):
                result += f"{i}. {detail}\n"
            result += "\n"
        
        if structured["注意事项"]:
            result += "4. 注意事项：\n"
            for i, note in enumerate(structured["注意事项"], 1):
                result += f"{i}. {note}\n"
            result += "\n"
        
        if structured["约束条件"]:
            result += "5. 约束条件：\n"
            for i, constraint in enumerate(structured["约束条件"], 1):
                result += f"{i}. {constraint}\n"
        
        return result
    
    def convert_to_markdown(self, structured):
        result = "# AI提示词结构\n\n"
        
        if structured["需求概述"]:
            result += f"## 1. 需求概述\n\n{structured['需求概述']}\n\n"
        
        if structured["功能要求"]:
            result += "## 2. 功能要求\n\n"
            for i, req in enumerate(structured["功能要求"], 1):
                result += f"{i}. {req}\n"
            result += "\n"
        
        if structured["技术细节"]:
            result += "## 3. 技术细节\n\n"
            for i, detail in enumerate(structured["技术细节"], 1):
                result += f"{i}. {detail}\n"
            result += "\n"
        
        if structured["注意事项"]:
            result += "## 4. 注意事项\n\n"
            for i, note in enumerate(structured["注意事项"], 1):
                result += f"{i}. {note}\n"
            result += "\n"
        
        if structured["约束条件"]:
            result += "## 5. 约束条件\n\n"
            for i, constraint in enumerate(structured["约束条件"], 1):
                result += f"{i}. {constraint}\n"
        
        return result
    
    def save_to_history(self, input_text, output_text):
        # 保存到历史记录
        category = self.template_category.get()
        self.cursor.execute("""
            INSERT INTO history (input_text, output_text, category)
            VALUES (?, ?, ?)
        """, (input_text, output_text, category))
        self.conn.commit()
        self.search_history()
    
    def update_analysis(self, text):
        # 更新关键词分析
        keywords = self.extract_keywords(text)
        self.keywords_text.delete(1.0, tk.END)
        self.keywords_text.insert(tk.END, "关键词：\n" + "\n".join(keywords))
        
        # 更新优化建议
        suggestions = self.generate_suggestions(text)
        self.suggestions_text.delete(1.0, tk.END)
        self.suggestions_text.insert(tk.END, "优化建议：\n" + "\n".join(suggestions))
        
        # 更新质量评分
        score = self.calculate_quality_score(text)
        self.score_text.delete(1.0, tk.END)
        self.score_text.insert(tk.END, f"质量评分：{score}/100")
    
    def extract_keywords(self, text):
        # 提取关键词
        keywords = set()
        for category, words in self.keyword_mapping.items():
            for word in words:
                if word in text:
                    keywords.add(f"{category}: {word}")
        return sorted(list(keywords))
    
    def generate_suggestions(self, text):
        # 生成优化建议
        suggestions = []
        
        # 检查长度
        if len(text) < 50:
            suggestions.append("提示词可能过短，建议补充更多细节")
        
        # 检查关键词
        if not any(word in text for words in self.keyword_mapping.values() for word in words):
            suggestions.append("建议添加更多技术关键词")
        
        # 检查结构
        if "。" not in text and "！" not in text:
            suggestions.append("建议使用更清晰的句子结构")
        
        return suggestions
    
    def calculate_quality_score(self, text):
        # 计算质量评分
        score = 0
        
        # 长度评分
        length = len(text)
        if length > 200:
            score += 30
        elif length > 100:
            score += 20
        elif length > 50:
            score += 10
        
        # 关键词评分
        keyword_count = sum(1 for words in self.keyword_mapping.values() for word in words if word in text)
        score += min(keyword_count * 10, 30)
        
        # 结构评分
        if "。" in text or "！" in text:
            score += 20
        
        # 完整性评分
        if "功能" in text or "需求" in text:
            score += 20
        
        return min(score, 100)
    
    def change_output_format(self, *args):
        # 切换输出格式
        if self.output_text.get(1.0, tk.END).strip():
            self.generate_prompt()
    
    def copy_to_clipboard(self):
        self.root.clipboard_clear()
        self.root.clipboard_append(self.output_text.get(1.0, tk.END))
    
    def clear_all(self):
        self.input_text.delete(1.0, tk.END)
        self.output_text.delete(1.0, tk.END)
        self.keywords_text.delete(1.0, tk.END)
        self.suggestions_text.delete(1.0, tk.END)
        self.score_text.delete(1.0, tk.END)
        # 清空自动保存内容
        self.cursor.execute("DELETE FROM preferences WHERE key = ?", ("last_input",))
        self.conn.commit()
    
    def export_prompt(self):
        # 获取当前输出内容
        content = self.output_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("导出失败", "没有可导出的内容！")
            return
        # 根据当前格式确定文件类型和后缀
        fmt = self.output_format.get()
        ext_map = {"Markdown": ".md", "JSON": ".json", "YAML": ".yaml", "文本": ".txt"}
        ext = ext_map.get(fmt, ".txt")
        default_name = f"prompt_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        file_path = filedialog.asksaveasfilename(defaultextension=ext, initialfile=default_name,
                                                 filetypes=[("所有文件", "*.*"), (f"{fmt}文件", f"*{ext}")])
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                messagebox.showinfo("导出成功", f"已成功导出到：\n{file_path}")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出时出错：{e}")
    
    def export_templates(self):
        # 导出当前分类的模板为json文件
        category = self.template_category.get()
        self.cursor.execute("SELECT name, content, created_at FROM templates WHERE category = ?", (category,))
        templates = self.cursor.fetchall()
        if not templates:
            messagebox.showwarning("导出模板", "当前分类下没有模板可导出！")
            return
        data = [{"name": t[0], "content": t[1], "created_at": t[2]} for t in templates]
        default_name = f"templates_{category}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        file_path = filedialog.asksaveasfilename(defaultextension=".json", initialfile=default_name,
                                                 filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")])
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("导出成功", f"模板已导出到：\n{file_path}")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出时出错：{e}")

    def import_templates(self):
        # 从json文件批量导入模板，自动去重
        file_path = filedialog.askopenfilename(filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")])
        if not file_path:
            return
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            count = 0
            category = self.template_category.get()
            for item in data:
                name = item.get("name")
                content = item.get("content")
                # 检查是否已存在同名模板
                self.cursor.execute("SELECT 1 FROM templates WHERE name = ? AND category = ?", (name, category))
                if not self.cursor.fetchone():
                    self.cursor.execute("INSERT INTO templates (category, name, content) VALUES (?, ?, ?)",
                                       (category, name, content))
                    count += 1
            self.conn.commit()
            self.load_templates()
            messagebox.showinfo("导入完成", f"成功导入{count}个新模板。")
        except Exception as e:
            messagebox.showerror("导入失败", f"导入时出错：{e}")
    
    def toggle_star(self, event):
        # 双击切换星标
        selection = self.history_list.curselection()
        if not selection:
            return
        history_text = self.history_list.get(selection[0])
        created_at = history_text.split(" - ")[0][2:]
        # 查询当前star状态
        self.cursor.execute("SELECT star FROM history WHERE created_at = ?", (created_at,))
        row = self.cursor.fetchone()
        if row:
            new_star = 0 if row[0] else 1
            self.cursor.execute("UPDATE history SET star = ? WHERE created_at = ?", (new_star, created_at))
            self.conn.commit()
            self.search_history()
    
    def __del__(self):
        # 关闭数据库连接
        if hasattr(self, 'conn'):
            self.conn.close()

    def auto_save_input(self, event=None):
        # 自动保存输入内容
        input_text = self.input_text.get(1.0, tk.END).strip()
        self.cursor.execute("INSERT INTO preferences (key, value) VALUES (?, ?)", ("last_input", input_text))
        self.conn.commit()

    def restore_input(self):
        # 自动恢复上次输入内容
        self.cursor.execute("SELECT value FROM preferences WHERE key = ?", ("last_input",))
        last_input = self.cursor.fetchone()
        if last_input:
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(tk.END, last_input[0])

if __name__ == "__main__":
    root = tk.Tk()
    app = PromptGenerator(root)
    root.mainloop() 