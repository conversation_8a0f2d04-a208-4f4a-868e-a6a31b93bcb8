import pandas as pd
import sqlite3
import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QProgressBar, QMessageBox, QGroupBox, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal

class DatabaseImporter:
    """数据库导入类，用于将Excel数据导入到SQLite数据库"""

    @staticmethod
    def add_tongdaxin_column(db_path, table_name, progress_callback=None):
        """添加通达信行业列到数据库表"""
        conn = None
        try:
            if progress_callback:
                progress_callback(0, "正在连接数据库...")

            # 连接到SQLite数据库，设置超时和隔离级别
            conn = sqlite3.connect(db_path, timeout=30.0)
            conn.isolation_level = None  # 自动提交模式
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE")

            if progress_callback:
                progress_callback(30, "正在检查表结构...")

            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                if conn:
                    conn.close()
                if progress_callback:
                    progress_callback(0, f"表 {table_name} 不存在")
                return False, f"表 {table_name} 不存在"

            # 检查列是否已存在
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns = [row[1] for row in cursor.fetchall()]

            if '通达信行业' in columns:
                cursor.execute("COMMIT")
                if conn:
                    conn.close()
                if progress_callback:
                    progress_callback(100, "通达信行业列已存在，无需添加")
                return True, "通达信行业列已存在，无需添加"

            if progress_callback:
                progress_callback(60, "正在添加通达信行业列...")

            # 添加列
            cursor.execute(f"ALTER TABLE [{table_name}] ADD COLUMN [通达信行业] TEXT")

            # 提交事务
            cursor.execute("COMMIT")

            # 关闭连接
            if conn:
                conn.close()

            if progress_callback:
                progress_callback(100, "通达信行业列添加成功")

            return True, "通达信行业列添加成功"

        except Exception as e:
            import traceback
            error_msg = f"添加通达信行业列时出错: {str(e)}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(0, error_msg)

            # 如果发生错误，尝试回滚事务
            try:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("ROLLBACK")
            except:
                pass

            # 确保关闭连接
            try:
                if conn:
                    conn.close()
            except:
                pass

            return False, error_msg

    @staticmethod
    def update_zf_from_excel(excel_path, db_path, table_name, progress_callback=None):
        """直接从Excel文件更新数据库中的涨幅数据"""
        conn = None
        try:
            if progress_callback:
                progress_callback(0, "正在连接数据库...")

            # 读取Excel文件
            if progress_callback:
                progress_callback(10, f"正在读取Excel文件: {excel_path}...")

            df = pd.read_excel(excel_path)

            if df.empty:
                return False, "Excel文件中没有数据"

            # 查找涨幅列，使用更灵活的条件
            zf_col = None

            # 首先尝试精确匹配
            possible_names = ['涨幅', '涨跌幅', '涨跌幅(%)', '涨幅(%)', '涨跌(%)', '涨幅(%)']
            for name in possible_names:
                if name in df.columns:
                    zf_col = name
                    break

            # 如果没有找到，尝试部分匹配
            if not zf_col:
                for col in df.columns:
                    # 检查列名是否包含"涨"或"跌"
                    if '涨' in col or '跌' in col:
                        zf_col = col
                        break



            if not zf_col:
                # 如果仍然没有找到，但Excel中有"涨幅"列，尝试使用它
                if '涨幅' in df.columns:
                    zf_col = '涨幅'
                else:
                    return False, "Excel文件中没有找到涨幅列"

            # 连接到SQLite数据库，设置超时和隔离级别
            conn = sqlite3.connect(db_path, timeout=30.0)
            conn.isolation_level = None  # 自动提交模式
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE")

            if progress_callback:
                progress_callback(30, "正在更新涨幅数据...")

            # 检查数据库表是否有涨幅列
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns_info = cursor.fetchall()
            has_zf_column = False
            for col_info in columns_info:
                if col_info[1] == '涨幅':
                    has_zf_column = True
                    break

            # 如果没有涨幅列，添加一个
            if not has_zf_column:
                if progress_callback:
                    progress_callback(40, "正在添加涨幅列...")
                cursor.execute(f"ALTER TABLE [{table_name}] ADD COLUMN [涨幅] REAL")

            # 更新涨幅数据
            update_count = 0
            error_count = 0

            for i, row in df.iterrows():
                if pd.notna(row['代码']) and pd.notna(row[zf_col]):
                    try:
                        # 处理涨幅值
                        zf_value = row[zf_col]

                        # 如果是字符串，尝试处理百分比
                        if isinstance(zf_value, str):
                            if '%' in zf_value:
                                zf_value = zf_value.replace('%', '').strip()

                        # 转换为浮点数
                        float_value = float(zf_value)

                        # 更新数据库
                        cursor.execute(f"""
                            UPDATE [{table_name}]
                            SET [涨幅] = ?
                            WHERE [代码] = ?
                        """, (float_value, row['代码']))

                        if cursor.rowcount > 0:
                            update_count += 1
                    except Exception as e:
                        error_count += 1

                # 更新进度
                if progress_callback and i % 10 == 0:
                    progress = 40 + int((i / len(df)) * 50)
                    progress_callback(progress, f"已更新 {update_count} 条记录...")

            # 提交事务
            cursor.execute("COMMIT")

            # 关闭连接
            if conn:
                conn.close()

            if progress_callback:
                progress_callback(100, f"涨幅数据更新完成，共更新 {update_count} 条记录，失败 {error_count} 条")

            return True, f"涨幅数据更新完成，共更新 {update_count} 条记录，失败 {error_count} 条"

        except Exception as e:
            import traceback
            error_msg = f"更新涨幅数据时出错: {str(e)}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(0, error_msg)

            # 如果发生错误，尝试回滚事务
            try:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("ROLLBACK")
            except:
                pass

            # 确保关闭连接
            try:
                if conn:
                    conn.close()
            except:
                pass

            return False, error_msg

    @staticmethod
    def fix_table_structure(db_path, table_name, progress_callback=None):
        """修复数据库表结构，确保涨幅列是REAL类型"""
        conn = None
        try:
            if progress_callback:
                progress_callback(0, "正在连接数据库...")

            # 连接到SQLite数据库，设置超时和隔离级别
            conn = sqlite3.connect(db_path, timeout=30.0)
            conn.isolation_level = None  # 自动提交模式
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE")

            if progress_callback:
                progress_callback(10, "正在检查表结构...")

            # 检查涨幅列的数据类型
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns_info = cursor.fetchall()

            # 查找涨幅列
            zf_column = None
            for col_info in columns_info:
                if col_info[1] == '涨幅':
                    zf_column = col_info
                    break

            # 如果涨幅列不存在或已经是REAL类型，则不需要修复
            if zf_column is None:
                conn.close()
                if progress_callback:
                    progress_callback(100, "表中没有涨幅列，无需修复")
                return True, "表中没有涨幅列，无需修复"

            if zf_column[2] == 'REAL':
                conn.close()
                if progress_callback:
                    progress_callback(100, "涨幅列已经是REAL类型，无需修复")
                return True, "涨幅列已经是REAL类型，无需修复"

            if progress_callback:
                progress_callback(20, "正在创建临时表...")

            # 创建新表结构
            new_columns = []
            for col_info in columns_info:
                col_name = col_info[1]
                col_type = col_info[2]
                if col_name == '涨幅':
                    col_type = 'REAL'  # 将涨幅列类型改为REAL
                if col_name == '代码':
                    new_columns.append(f"[{col_name}] {col_type} PRIMARY KEY")
                else:
                    new_columns.append(f"[{col_name}] {col_type}")

            # 创建临时表，先检查并删除可能已存在的临时表
            temp_table = f"{table_name}_temp"

            # 检查临时表是否已存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{temp_table}'")
            if cursor.fetchone():
                cursor.execute(f"DROP TABLE IF EXISTS [{temp_table}]")

            # 创建新的临时表
            create_temp_table_sql = f"CREATE TABLE [{temp_table}] ({', '.join(new_columns)})"
            cursor.execute(create_temp_table_sql)

            if progress_callback:
                progress_callback(40, "正在复制数据到临时表...")

            # 复制数据到临时表，但对涨幅列进行特殊处理
            # 首先，查询原表中的数据
            cursor.execute(f"SELECT * FROM [{table_name}]")
            rows = cursor.fetchall()

            # 获取列名
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns_info = cursor.fetchall()
            column_names = [col_info[1] for col_info in columns_info]

            # 找到涨幅列的索引
            zf_index = -1
            for i, col_name in enumerate(column_names):
                if col_name == '涨幅':
                    zf_index = i
                    break



            # 如果找到涨幅列，则对每一行数据进行处理
            if zf_index >= 0:
                # 准备插入语句
                placeholders = ', '.join(['?'] * len(column_names))
                insert_sql = f"INSERT INTO [{temp_table}] ({', '.join([f'[{col}]' for col in column_names])}) VALUES ({placeholders})"

                # 处理每一行数据
                for row in rows:
                    # 转换为列表，以便修改
                    values = list(row)

                    # 处理涨幅列
                    zf_value = values[zf_index]
                    # 尝试转换为浮点数
                    if zf_value is not None:
                        try:
                            # 如果是字符串，尝试处理百分比
                            if isinstance(zf_value, str):
                                if '%' in zf_value:
                                    zf_value = zf_value.replace('%', '').strip()

                            # 转换为浮点数
                            float_value = float(zf_value)
                            values[zf_index] = float_value
                        except (ValueError, TypeError) as e:
                            values[zf_index] = None

                    # 执行插入
                    cursor.execute(insert_sql, values)
            else:
                # 如果没有找到涨幅列，则直接复制数据
                columns_str = ', '.join([f'[{col_info[1]}]' for col_info in columns_info])
                cursor.execute(f"""
                    INSERT INTO [{temp_table}] ({columns_str})
                    SELECT {columns_str} FROM [{table_name}]
                """)

            if progress_callback:
                progress_callback(60, "正在删除原表...")

            # 删除原表
            cursor.execute(f"DROP TABLE [{table_name}]")

            if progress_callback:
                progress_callback(80, "正在重命名临时表...")

            # 重命名临时表
            cursor.execute(f"ALTER TABLE [{temp_table}] RENAME TO [{table_name}]")

            # 提交事务
            cursor.execute("COMMIT")

            # 关闭连接
            if conn:
                conn.close()

            if progress_callback:
                progress_callback(100, "表结构修复完成")

            return True, "表结构修复完成，涨幅列已更改为REAL类型"

        except Exception as e:
            import traceback
            error_msg = f"修复表结构时出错: {str(e)}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(0, error_msg)

            # 如果发生错误，尝试回滚事务
            try:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("ROLLBACK")
            except:
                pass

            # 确保关闭连接
            try:
                if conn:
                    conn.close()
            except:
                pass

            return False, error_msg

    @staticmethod
    def remove_duplicates(db_path, table_name, progress_callback=None):
        """删除数据库中的重复记录，以代码列为主键"""
        conn = None
        try:
            if progress_callback:
                progress_callback(0, "正在连接数据库...")

            # 连接到SQLite数据库，设置超时和隔离级别
            conn = sqlite3.connect(db_path, timeout=30.0)
            conn.isolation_level = None  # 自动提交模式
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE")

            if progress_callback:
                progress_callback(30, "正在查找重复记录...")

            # 查询重复的代码
            cursor.execute(f"""
                SELECT 代码, COUNT(*) as count
                FROM [{table_name}]
                GROUP BY 代码
                HAVING count > 1
            """)

            duplicates = cursor.fetchall()

            if not duplicates:
                cursor.execute("COMMIT")
                if conn:
                    conn.close()
                if progress_callback:
                    progress_callback(100, "未发现重复记录")
                return True, "未发现重复记录"

            if progress_callback:
                progress_callback(60, f"发现 {len(duplicates)} 个代码有重复记录，正在删除...")

            # 使用更安全的方法删除重复记录
            # 对于每个重复的代码，保留一条记录，删除其他记录
            for code, _ in duplicates:  # 使用 _ 忽略 count 变量
                # 查找该代码的所有记录ID
                cursor.execute(f"""
                    SELECT rowid FROM [{table_name}]
                    WHERE 代码 = ?
                    ORDER BY rowid
                """, (code,))

                rowids = cursor.fetchall()
                # 保留第一条记录，删除其他记录
                for rowid in rowids[1:]:
                    cursor.execute(f"""
                        DELETE FROM [{table_name}]
                        WHERE rowid = ?
                    """, (rowid[0],))

            # 提交事务
            cursor.execute("COMMIT")

            # 获取删除后的记录数
            cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
            final_count = cursor.fetchone()[0]

            # 计算删除的记录数
            removed_count = sum([count - 1 for _, count in duplicates])

            if conn:
                conn.close()

            if progress_callback:
                progress_callback(100, f"成功删除 {removed_count} 条重复记录，当前共有 {final_count} 条记录")

            return True, f"成功删除 {removed_count} 条重复记录，当前共有 {final_count} 条记录"

        except Exception as e:
            import traceback
            error_msg = f"删除重复记录时出错: {str(e)}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(0, error_msg)

            # 如果发生错误，尝试回滚事务
            try:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("ROLLBACK")
            except:
                pass

            # 确保关闭连接
            try:
                if conn:
                    conn.close()
            except:
                pass

            return False, error_msg

    @staticmethod
    def import_excel_to_db(excel_path, db_path, table_name, progress_callback=None):
        """将Excel数据导入到SQLite数据库"""
        conn = None
        try:
            # 检查Excel文件是否存在
            if not os.path.exists(excel_path):
                return False, f"Excel文件不存在: {excel_path}"

            if progress_callback:
                progress_callback(10, f"正在读取Excel文件: {excel_path}...")

            # 读取Excel文件
            df = pd.read_excel(excel_path)

            if df.empty:
                return False, "Excel文件中没有数据"

            # 查找涨幅列，使用更灵活的条件
            zf_col = None

            # 首先尝试精确匹配
            possible_names = ['涨幅', '涨跌幅', '涨跌幅(%)', '涨幅(%)', '涨跌(%)', '涨幅(%)']
            for name in possible_names:
                if name in df.columns:
                    zf_col = name
                    break

            # 如果没有找到，尝试部分匹配
            if not zf_col:
                for col in df.columns:
                    # 检查列名是否包含"涨"或"跌"
                    if '涨' in col or '跌' in col:
                        zf_col = col
                        break



            if progress_callback:
                progress_callback(30, "正在连接数据库...")

            # 连接到SQLite数据库，设置超时和隔离级别
            conn = sqlite3.connect(db_path, timeout=30.0)
            conn.isolation_level = None  # 自动提交模式
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN IMMEDIATE")

            # 检查是否有'代码'列，用作主键
            if '代码' not in df.columns:
                conn.close()
                return False, "Excel文件中没有'代码'列，无法导入"

            if progress_callback:
                progress_callback(50, "正在创建或更新数据表...")

            # 检查表是否存在
            cursor = conn.cursor()
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            table_exists = cursor.fetchone() is not None

            # 创建表（如果不存在）
            if not table_exists:
                # 根据DataFrame的列创建表结构
                columns = []
                for col in df.columns:
                    # 确定列的数据类型
                    if col == '代码':
                        columns.append(f"[{col}] TEXT PRIMARY KEY")
                    elif col == '涨幅':
                        columns.append(f"[{col}] REAL")  # 确保涨幅列是REAL类型
                    elif df[col].dtype == 'int64' or df[col].dtype == 'float64':
                        columns.append(f"[{col}] REAL")
                    else:
                        columns.append(f"[{col}] TEXT")

                # 添加通达信行业列
                columns.append("[通达信行业] TEXT")

                create_table_sql = f"CREATE TABLE IF NOT EXISTS [{table_name}] ({', '.join(columns)})"
                conn.execute(create_table_sql)
            else:
                # 表已存在，检查是否有涨幅列
                cursor.execute(f"PRAGMA table_info([{table_name}])")
                existing_columns = [row[1] for row in cursor.fetchall()]

                # 检查是否需要添加涨幅列
                if '涨幅' not in existing_columns and '涨幅' in df.columns:
                    conn.execute(f"ALTER TABLE [{table_name}] ADD COLUMN [涨幅] REAL")

                # 检查是否需要添加通达信行业列
                if '通达信行业' not in existing_columns:
                    conn.execute(f"ALTER TABLE [{table_name}] ADD COLUMN [通达信行业] TEXT")
                    print("已添加通达信行业列")

            if progress_callback:
                progress_callback(70, "正在导入数据...")

            # 将数据导入到表中
            # 使用INSERT OR REPLACE语句，如果代码已存在则替换整行
            for i, row in df.iterrows():
                # 构建INSERT OR REPLACE语句
                placeholders = ', '.join(['?'] * len(df.columns))
                columns_str = ', '.join([f'[{col}]' for col in df.columns])
                insert_sql = f"INSERT OR REPLACE INTO [{table_name}] ({columns_str}) VALUES ({placeholders})"

                # 准备数据
                values = []
                for col in df.columns:
                    # 检查是否是涨幅列
                    is_zf_col = False
                    if col == '涨幅':
                        is_zf_col = True
                    elif zf_col and col == zf_col:
                        is_zf_col = True

                    if is_zf_col and pd.notna(row[col]):
                        # 尝试将涨幅转换为浮点数
                        try:
                            # 处理可能的百分比字符串，如"-2.256%"
                            value_str = str(row[col])

                            # 如果是百分比字符串，去掉百分号
                            if '%' in value_str:
                                value_str = value_str.replace('%', '').strip()

                            # 尝试转换为浮点数
                            float_value = float(value_str)

                            # 添加到值列表
                            values.append(float_value)
                        except (ValueError, TypeError) as e:
                            values.append(None)
                    else:
                        values.append(row[col] if pd.notna(row[col]) else None)

                # 执行插入
                conn.execute(insert_sql, values)

            # 提交事务
            cursor.execute("COMMIT")

            if progress_callback:
                progress_callback(90, "正在验证导入结果...")

            # 获取本次实际导入的记录数
            actual_import_count = len(df)

            # 获取数据库中的总记录数（仅供参考）
            cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
            total_count = cursor.fetchone()[0]

            # 关闭连接
            if conn:
                conn.close()

            if progress_callback:
                progress_callback(95, f"导入完成，本次导入 {actual_import_count} 条记录，数据库现有 {total_count} 条记录")

            # 导入完成
            if progress_callback:
                progress_callback(100, "导入完成")

            return True, f"成功导入 {actual_import_count} 条记录到数据库，数据库现有 {total_count} 条记录"

        except Exception as e:
            import traceback
            error_msg = f"导入数据时出错: {str(e)}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(0, error_msg)

            # 如果发生错误，尝试回滚事务
            try:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("ROLLBACK")
            except:
                pass

            # 确保关闭连接
            try:
                if conn:
                    conn.close()
            except:
                pass

            return False, error_msg

class DatabaseImporterWidget(QWidget):
    """数据库导入界面，用于集成到主GUI"""

    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建标题
        title_label = QLabel('Excel导入数据库')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建文件信息区域
        info_group = QGroupBox('文件信息')
        info_layout = QVBoxLayout()

        self.excel_path_label = QLabel('Excel文件: db.xlsx')
        self.db_path_label = QLabel('数据库文件: stock.db')
        self.table_name_label = QLabel('数据表: stocks')

        info_layout.addWidget(self.excel_path_label)
        info_layout.addWidget(self.db_path_label)
        info_layout.addWidget(self.table_name_label)
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)

        # 创建操作按钮
        buttons_layout = QHBoxLayout()

        self.import_button = QPushButton('导入数据')
        self.import_button.clicked.connect(self.import_data)

        self.clean_button = QPushButton('清理重复数据')
        self.clean_button.clicked.connect(self.clean_duplicates)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.import_button)
        buttons_layout.addWidget(self.clean_button)
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

        # 创建进度条
        progress_group = QGroupBox('导入进度')
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        self.status_label = QLabel('准备就绪')

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # 创建日志区域
        log_group = QGroupBox('操作日志')
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置布局
        self.setLayout(main_layout)

    def update_progress(self, value, message):
        """更新进度条和状态消息"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def import_data(self):
        """导入数据到数据库"""
        excel_path = 'db.xlsx'
        db_path = 'stock.db'
        table_name = 'stocks'

        # 检查Excel文件是否存在
        if not os.path.exists(excel_path):
            QMessageBox.critical(self, '错误', f'Excel文件不存在: {excel_path}')
            return

        # 禁用按钮
        self.import_button.setEnabled(False)
        self.clean_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)

        # 添加日志
        self.log_text.append(f"开始导入 {excel_path} 到 {db_path} 的 {table_name} 表...")

        # 导入数据
        success, message = DatabaseImporter.import_excel_to_db(
            excel_path, db_path, table_name, self.update_progress)

        # 启用按钮
        self.import_button.setEnabled(True)
        self.clean_button.setEnabled(True)
        self.add_column_button.setEnabled(True)

        # 显示结果
        if success:
            QMessageBox.information(self, '成功', message)
        else:
            QMessageBox.critical(self, '错误', message)

        # 添加日志
        self.log_text.append(f"导入结果: {'成功' if success else '失败'} - {message}")

    def clean_duplicates(self):
        """清理数据库中的重复记录"""
        db_path = 'stock.db'
        table_name = 'stocks'

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            QMessageBox.critical(self, '错误', f'数据库文件不存在: {db_path}')
            return

        # 禁用按钮
        self.import_button.setEnabled(False)
        self.clean_button.setEnabled(False)
        self.add_column_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)

        # 添加日志
        self.log_text.append(f"开始清理 {db_path} 中 {table_name} 表的重复记录...")

        # 清理重复记录
        success, message = DatabaseImporter.remove_duplicates(
            db_path, table_name, self.update_progress)

        # 启用按钮
        self.import_button.setEnabled(True)
        self.clean_button.setEnabled(True)
        self.add_column_button.setEnabled(True)

        # 显示结果
        if success:
            QMessageBox.information(self, '成功', message)
        else:
            QMessageBox.critical(self, '错误', message)

        # 添加日志
        self.log_text.append(f"清理结果: {'成功' if success else '失败'} - {message}")

    def add_tongdaxin_column(self):
        """添加通达信行业列到数据库表"""
        db_path = 'stock.db'
        table_name = 'stocks'

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            QMessageBox.critical(self, '错误', f'数据库文件不存在: {db_path}')
            return

        # 禁用按钮
        self.import_button.setEnabled(False)
        self.clean_button.setEnabled(False)
        self.add_column_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)

        # 添加日志
        self.log_text.append(f"开始添加通达信行业列到 {db_path} 的 {table_name} 表...")

        # 添加列
        success, message = DatabaseImporter.add_tongdaxin_column(
            db_path, table_name, self.update_progress)

        # 启用按钮
        self.import_button.setEnabled(True)
        self.clean_button.setEnabled(True)
        self.add_column_button.setEnabled(True)

        # 显示结果
        if success:
            QMessageBox.information(self, '成功', message)
        else:
            QMessageBox.critical(self, '错误', message)

        # 添加日志
        self.log_text.append(f"添加列结果: {'成功' if success else '失败'} - {message}")



# 测试代码
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    window = DatabaseImporterWidget()
    window.show()
    sys.exit(app.exec_())
