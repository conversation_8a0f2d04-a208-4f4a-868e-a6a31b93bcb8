#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
开盘啦数据管理工具 - PyQt5界面
包含涨停数据和题材数据功能
"""

import os
import sys
import threading
import subprocess
import sqlite3
import pandas as pd
import tushare as ts
import configparser
import time
from datetime import datetime, timedelta
# 导入数据库工具类
from db_utils import DbUtils
from db_path_util import get_db_path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QPushButton, QComboBox, QProgressBar, QTextEdit, 
                           QStatusBar, QFrame, QGroupBox, QMessageBox, QScrollBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont


class FetchDataThread(QThread):
    """数据获取线程"""
    # 信号定义
    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)
    finished_signal = pyqtSignal(bool, str, int)
    
    def __init__(self, parent, fetch_type, days, tag=None):
        super().__init__(parent)
        self.fetch_type = fetch_type  # 'limit', 'concept', 'concept_stocks'
        self.days = days
        self.tag = tag
        self.parent = parent
    
    def run(self):
        try:
            # 获取配置信息
            config = self.parent.get_config()
            if not config:
                self.log_signal.emit("错误: 无法读取配置文件")
                self.status_signal.emit("获取数据失败: 无法读取配置文件")
                self.finished_signal.emit(False, "无法读取配置文件", 0)
                return
                
            # 尝试从不同的配置节获取token
            token = ''
            if config.has_section('Tushare'):
                token = config.get('Tushare', 'token', fallback='')
            if not token and config.has_section('API'):
                token = config.get('API', 'token', fallback='')
            
            if not token:
                self.log_signal.emit("错误: 未配置Tushare Token，请先配置后再试")
                self.status_signal.emit("获取数据失败: 未配置Token")
                self.finished_signal.emit(False, "未配置Token", 0)
                return
            
            # 初始化Tushare
            ts.set_token(token)
            pro = ts.pro_api()
            
            # 获取当前日期
            today = datetime.now()
            end_date = today.strftime('%Y%m%d')
            
            # 计算开始日期
            start_date = (today - timedelta(days=self.days)).strftime('%Y%m%d')
            
            # 获取交易日历
            trade_dates = self.parent.get_trade_dates(pro, start_date, end_date)
            
            # 如果没有获取到交易日历，则使用日期范围
            if not trade_dates:
                self.log_signal.emit("未获取到交易日历，使用日期范围")
                # 创建日期范围
                date_range = []
                current_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                while current_dt <= end_dt:
                    date_range.append(current_dt.strftime('%Y%m%d'))
                    current_dt += timedelta(days=1)
                trade_dates = date_range
            
            # 获取数据库路径
            db_path = get_db_path()
            self.log_signal.emit(f"使用数据库: {db_path}")
            
            # 获取当前时间
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 存储所有数据
            total_records = 0
            
            # 获取每个交易日的数据
            total_dates = len(trade_dates)
            
            # 根据不同的数据类型获取数据
            if self.fetch_type == 'limit':
                self.fetch_limit_data(pro, trade_dates, total_dates, now, total_records)
            elif self.fetch_type == 'concept':
                self.fetch_concept_data(pro, trade_dates, total_dates, now, total_records)
            elif self.fetch_type == 'concept_stocks':
                self.fetch_concept_stocks_data(pro, trade_dates, total_dates, now, total_records)
            
        except Exception as e:
            self.log_signal.emit(f"获取数据时发生错误: {e}")
            self.status_signal.emit(f"获取{self.get_fetch_type_name()}失败")
            self.finished_signal.emit(False, str(e), 0)
    
    def fetch_limit_data(self, pro, trade_dates, total_dates, now, total_records):
        """获取涨停数据"""
        for i, date in enumerate(trade_dates):
            try:
                self.log_signal.emit(f"正在获取 {date} 的{self.tag}数据 ({i+1}/{total_dates})...")
                self.status_signal.emit(f"正在获取 {date} 的{self.tag}数据 ({i+1}/{total_dates})...")
                self.progress_signal.emit(i, total_dates)
                
                # 调用API获取数据
                df = pro.kpl_list(trade_date=date, tag=self.tag, fields='ts_code,name,trade_date,tag,theme,status')
                
                if df is not None and not df.empty:
                    self.log_signal.emit(f"成功获取 {len(df)} 条数据")
                    
                    # 逐条插入数据
                    conn = sqlite3.connect(get_db_path())
                    cursor = conn.cursor()
                    for _, row in df.iterrows():
                        cursor.execute('''
                        INSERT OR REPLACE INTO kpl_limit_list
                        (ts_code, name, trade_date, tag, theme, status, update_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            row['ts_code'],
                            row['name'],
                            row['trade_date'],
                            row['tag'],
                            row.get('theme', ''),
                            row.get('status', ''),
                            now
                        ))
                        total_records += 1
                    conn.commit()
                    conn.close()
                    
                else:
                    self.log_signal.emit(f"未获取到 {date} 的数据")
                
                # 添加延迟，避免超过API调用限制
                time.sleep(0.5)
            except Exception as e:
                self.log_signal.emit(f"获取 {date} 的数据失败: {e}")
                # 如果失败，继续下一个日期
                continue
        
        # 更新进度条到100%
        self.progress_signal.emit(total_dates, total_dates)
        
        self.log_signal.emit(f"数据获取完成，共导入 {total_records} 条记录到数据库")
        self.status_signal.emit(f"数据获取完成，共导入 {total_records} 条记录")
        
        # 发送完成信号
        self.finished_signal.emit(True, "成功", total_records)
    
    def fetch_concept_data(self, pro, trade_dates, total_dates, now, total_records):
        """获取题材数据"""
        for i, date in enumerate(trade_dates):
            try:
                self.log_signal.emit(f"正在获取 {date} 的题材数据 ({i+1}/{total_dates})...")
                self.status_signal.emit(f"正在获取 {date} 的题材数据 ({i+1}/{total_dates})...")
                self.progress_signal.emit(i, total_dates)
                
                # 调用API获取数据
                df = pro.kpl_concept(trade_date=date)
                
                if df is not None and not df.empty:
                    self.log_signal.emit(f"成功获取 {len(df)} 条题材数据")
                    
                    # 逐条插入数据
                    conn = sqlite3.connect(get_db_path())
                    cursor = conn.cursor()
                    for _, row in df.iterrows():
                        cursor.execute('''
                        INSERT OR REPLACE INTO kpl_concepts
                        (ts_code, name, z_t_num, up_num, last_update)
                        VALUES (?, ?, ?, ?, ?)
                        ''', (
                            row['ts_code'],
                            row['name'],
                            row.get('z_t_num', 0),
                            row.get('up_num', 0),
                            row['trade_date']
                        ))
                        total_records += 1
                    conn.commit()
                    conn.close()
                    
                else:
                    self.log_signal.emit(f"未获取到 {date} 的题材数据")
                
                # 添加延迟，避免超过API调用限制
                time.sleep(0.5)
            except Exception as e:
                self.log_signal.emit(f"获取 {date} 的题材数据失败: {e}")
                # 如果失败，继续下一个日期
                continue
        
        # 更新进度条到100%
        self.progress_signal.emit(total_dates, total_dates)
        
        self.log_signal.emit(f"题材数据获取完成，共导入 {total_records} 条记录到数据库")
        self.status_signal.emit(f"题材数据获取完成，共导入 {total_records} 条记录")
        
        # 发送完成信号
        self.finished_signal.emit(True, "成功", total_records)
    
    def fetch_concept_stocks_data(self, pro, trade_dates, total_dates, now, total_records):
        """获取题材成分数据"""
        for i, date in enumerate(trade_dates):
            try:
                self.log_signal.emit(f"正在获取 {date} 的题材成分数据 ({i+1}/{total_dates})...")
                self.status_signal.emit(f"正在获取 {date} 的题材成分数据 ({i+1}/{total_dates})...")
                self.progress_signal.emit(i, total_dates)
                
                # 调用API获取数据
                df = pro.kpl_concept_cons(trade_date=date)
                
                if df is not None and not df.empty:
                    self.log_signal.emit(f"成功获取 {len(df)} 条题材成分数据")
                    
                    # 逐条插入数据
                    conn = sqlite3.connect(get_db_path())
                    cursor = conn.cursor()
                    for _, row in df.iterrows():
                        cursor.execute('''
                        INSERT OR REPLACE INTO kpl_concept_stocks
                        (concept_name, ts_code, stock_name, in_date)
                        VALUES (?, ?, ?, ?)
                        ''', (
                            row['name'],
                            row['con_code'],
                            row.get('con_name', ''),
                            row['trade_date']
                        ))
                        total_records += 1
                    conn.commit()
                    conn.close()
                    
                else:
                    self.log_signal.emit(f"未获取到 {date} 的题材成分数据")
                
                # 添加延迟，避免超过API调用限制
                time.sleep(0.5)
            except Exception as e:
                self.log_signal.emit(f"获取 {date} 的题材成分数据失败: {e}")
                # 如果失败，继续下一个日期
                continue
        
        # 更新进度条到100%
        self.progress_signal.emit(total_dates, total_dates)
        
        self.log_signal.emit(f"题材成分数据获取完成，共导入 {total_records} 条记录到数据库")
        self.status_signal.emit(f"题材成分数据获取完成，共导入 {total_records} 条记录")
        
        # 发送完成信号
        self.finished_signal.emit(True, "成功", total_records)
    
    def get_fetch_type_name(self):
        """获取数据类型名称"""
        if self.fetch_type == 'limit':
            return "涨停数据"
        elif self.fetch_type == 'concept':
            return "题材数据"
        elif self.fetch_type == 'concept_stocks':
            return "题材成分数据"
        return "数据"


class KplDataQtWidget(QWidget):
    """开盘啦数据管理工具部件"""
    
    # 定义状态更新信号，用于更新主窗口状态栏
    status_update_signal = pyqtSignal(str)

    def __init__(self, parent=None):
        """初始化GUI界面"""
        super().__init__(parent)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        
        # 创建标题标签
        title_label = QLabel("开盘啦数据管理工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.main_layout.addWidget(title_label)
        
        # 创建按钮区域
        self.create_button_area()
        
        # 创建参数设置区域
        self.create_param_area()
        
        # 创建进度条区域
        self.create_progress_area()
        
        # 创建日志区域
        self.create_log_area()
        
        # 数据获取线程
        self.fetch_thread = None
        
        # 获取配置信息
        self.get_config()
    
    def create_button_area(self):
        """创建按钮区域"""
        # 创建按钮组
        button_group = QGroupBox("数据获取")
        button_layout = QHBoxLayout()
        
        # 获取涨停数据按钮
        self.fetch_limit_data_button = QPushButton("获取涨停数据")
        self.fetch_limit_data_button.clicked.connect(self.fetch_limit_data)
        button_layout.addWidget(self.fetch_limit_data_button)
        
        # 获取题材数据按钮
        self.fetch_concept_data_button = QPushButton("获取题材数据")
        self.fetch_concept_data_button.clicked.connect(self.fetch_concept_data)
        button_layout.addWidget(self.fetch_concept_data_button)
        
        # 获取题材成分按钮
        self.fetch_concept_stocks_button = QPushButton("获取题材成分")
        self.fetch_concept_stocks_button.clicked.connect(self.fetch_concept_stocks)
        button_layout.addWidget(self.fetch_concept_stocks_button)
        
        button_group.setLayout(button_layout)
        self.main_layout.addWidget(button_group)
    
    def create_param_area(self):
        """创建参数设置区域"""
        # 创建参数组
        param_group = QGroupBox("参数设置")
        param_layout = QHBoxLayout()
        
        # 天数选择
        days_layout = QHBoxLayout()
        days_label = QLabel("获取天数:")
        self.days_combo = QComboBox()
        self.days_combo.addItems(["1", "3", "7", "14", "30", "60", "90", "180", "365"])
        self.days_combo.setCurrentIndex(2)  # 默认选择7天
        days_layout.addWidget(days_label)
        days_layout.addWidget(self.days_combo)
        param_layout.addLayout(days_layout)
        
        # 板单类型
        tag_layout = QHBoxLayout()
        tag_label = QLabel("板单类型:")
        self.tag_combo = QComboBox()
        self.tag_combo.addItems(["涨停", "炸板", "跌停", "自然涨停", "竞价"])
        tag_layout.addWidget(tag_label)
        tag_layout.addWidget(self.tag_combo)
        param_layout.addLayout(tag_layout)
        
        param_group.setLayout(param_layout)
        self.main_layout.addWidget(param_group)
    
    def create_progress_area(self):
        """创建进度条区域"""
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        self.main_layout.addLayout(progress_layout)
    
    def create_log_area(self):
        """创建日志区域"""
        # 创建日志组
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        
        # 创建日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        self.main_layout.addWidget(log_group)
    
    def log(self, message):
        """添加日志"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_text.append(f"[{current_time}] {message}")
        # 滚动到最新的日志
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def update_status(self, message):
        """更新状态栏"""
        # 发送信号到主窗口
        self.status_update_signal.emit(message)
    
    def update_progress(self, value, max_value):
        """更新进度条"""
        if max_value > 0:
            progress = int((value / max_value) * 100)
            self.progress_bar.setValue(progress)
        else:
            self.progress_bar.setValue(0)
    
    def get_config(self):
        """获取配置信息"""
        try:
            # 读取配置文件
            config = configparser.ConfigParser()
            
            # 尝试读取配置文件
            config_path = 'config.ini'
            if not os.path.exists(config_path):
                # 如果当前目录没有，尝试上级目录
                parent_dir = os.path.dirname(os.path.abspath(__file__))
                config_path = os.path.join(parent_dir, 'config.ini')
            
            if not os.path.exists(config_path):
                self.log(f"错误: 找不到配置文件 {config_path}")
                return None
                
            self.log(f"读取配置文件: {config_path}")
            config.read(config_path, encoding='utf-8')
            
            # 检查是否有Tushare token
            if config.has_section('Tushare') and config.has_option('Tushare', 'token'):
                token = config.get('Tushare', 'token')
                self.log(f"成功读取Tushare token")
            elif config.has_section('API') and config.has_option('API', 'token'):
                token = config.get('API', 'token')
                self.log(f"成功读取API token")
            else:
                self.log("警告: 配置文件中没有找到Tushare token")
            
            self.config = config
            return config
        except Exception as e:
            self.log(f"读取配置文件失败: {e}")
            return None
    
    def get_db_path(self):
        """获取数据库路径"""
        # 使用统一的数据库路径函数
        return get_db_path()
    
    def get_trade_dates(self, pro, start_date, end_date):
        """获取交易日历"""
        try:
            # 获取交易日历
            df = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date, is_open=1)
            if df is not None and not df.empty:
                # 返回交易日列表
                return df['cal_date'].tolist()
            return []
        except Exception as e:
            self.log(f"获取交易日历失败: {e}")
            return []
    
    def fetch_limit_data(self):
        """获取涨停数据并保存到数据库"""
        # 获取参数
        days = int(self.days_combo.currentText())
        tag = self.tag_combo.currentText()
        
        self.log(f"开始获取 {days} 天的{tag}数据...")
        self.update_status(f"正在获取 {days} 天的{tag}数据...")
        
        # 禁用按钮
        self.fetch_limit_data_button.setEnabled(False)
        
        # 创建并启动线程
        self.fetch_thread = FetchDataThread(self, 'limit', days, tag)
        self.fetch_thread.log_signal.connect(self.log)
        self.fetch_thread.status_signal.connect(self.update_status)
        self.fetch_thread.progress_signal.connect(self.update_progress)
        self.fetch_thread.finished_signal.connect(self.on_fetch_finished)
        self.fetch_thread.start()
    
    def fetch_concept_data(self):
        """获取题材数据并保存到数据库"""
        # 获取参数
        days = int(self.days_combo.currentText())
        
        self.log(f"开始获取 {days} 天的题材数据...")
        self.update_status(f"正在获取 {days} 天的题材数据...")
        
        # 禁用按钮
        self.fetch_concept_data_button.setEnabled(False)
        
        # 创建并启动线程
        self.fetch_thread = FetchDataThread(self, 'concept', days)
        self.fetch_thread.log_signal.connect(self.log)
        self.fetch_thread.status_signal.connect(self.update_status)
        self.fetch_thread.progress_signal.connect(self.update_progress)
        self.fetch_thread.finished_signal.connect(self.on_fetch_finished)
        self.fetch_thread.start()
    
    def fetch_concept_stocks(self):
        """获取题材成分数据并保存到数据库"""
        # 获取参数
        days = int(self.days_combo.currentText())
        
        self.log(f"开始获取 {days} 天的题材成分数据...")
        self.update_status(f"正在获取 {days} 天的题材成分数据...")
        
        # 禁用按钮
        self.fetch_concept_stocks_button.setEnabled(False)
        
        # 创建并启动线程
        self.fetch_thread = FetchDataThread(self, 'concept_stocks', days)
        self.fetch_thread.log_signal.connect(self.log)
        self.fetch_thread.status_signal.connect(self.update_status)
        self.fetch_thread.progress_signal.connect(self.update_progress)
        self.fetch_thread.finished_signal.connect(self.on_fetch_finished)
        self.fetch_thread.start()
    
    def on_fetch_finished(self, success, message, total_records):
        """数据获取完成回调"""
        # 恢复按钮状态
        self.fetch_limit_data_button.setEnabled(True)
        self.fetch_concept_data_button.setEnabled(True)
        self.fetch_concept_stocks_button.setEnabled(True)
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "成功", f"成功导入 {total_records} 条记录到数据库")
        else:
            QMessageBox.critical(self, "错误", f"获取数据失败: {message}")


# 如果单独运行此文件，则创建一个测试窗口
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("开盘啦数据管理工具")
    window.resize(800, 600)
    
    widget = KplDataQtWidget()
    window.setCentralWidget(widget)
    
    window.show()
    sys.exit(app.exec_())
