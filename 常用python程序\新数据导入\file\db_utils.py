#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库工具类
提供数据库路径获取和同步更新功能
"""

import os
import sqlite3
import shutil
import time
from datetime import datetime
import configparser


class DbUtils:
    """数据库工具类"""
    
    @staticmethod
    def get_db_paths(db_name='stock.db'):
        """
        获取数据库路径列表
        返回当前目录和父目录中的数据库路径
        
        Args:
            db_name: 数据库文件名，默认为stock.db
            
        Returns:
            包含当前目录和父目录中数据库路径的列表
        """
        # 首先尝试从config.ini读取配置
        config_path = 'config.ini'
        if not os.path.exists(config_path):
            # 如果当前目录没有，尝试上级目录
            parent_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(parent_dir, 'config.ini')
        
        # 如果找到配置文件，尝试读取数据库路径
        if os.path.exists(config_path):
            try:
                config = configparser.ConfigParser()
                config.read(config_path, encoding='utf-8')
                if config.has_section('Path') and config.has_option('Path', 'db_path'):
                    db_path = config.get('Path', 'db_path')
                    return [db_path]
            except:
                pass  # 如果读取失败，回退到默认路径
        
        # 当前目录路径
        current_db_path = os.path.abspath(db_name)
        
        # 父目录路径
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        parent_db_path = os.path.join(parent_dir, db_name)
        
        return [current_db_path, parent_db_path]
    
    @staticmethod
    def execute_sql(sql, params=None, db_name='stock.db', commit=True, return_cursor=False):
        """
        在所有数据库路径上执行SQL语句
        
        Args:
            sql: SQL语句
            params: SQL参数
            db_name: 数据库文件名
            commit: 是否提交事务
            return_cursor: 是否返回游标对象
            
        Returns:
            如果return_cursor为True，返回游标对象列表；否则返回None
        """
        db_paths = DbUtils.get_db_paths(db_name)
        cursors = []
        connections = []
        
        for db_path in db_paths:
            try:
                # 确保数据库目录存在
                db_dir = os.path.dirname(db_path)
                if not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                
                # 连接数据库
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 执行SQL
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # 提交事务
                if commit:
                    conn.commit()
                
                # 添加到列表
                connections.append(conn)
                cursors.append(cursor)
            except Exception as e:
                print(f"执行SQL出错 ({db_path}): {e}")
                # 关闭之前成功的连接
                for conn in connections:
                    try:
                        conn.close()
                    except:
                        pass
                raise e
        
        if return_cursor:
            return cursors, connections
        else:
            # 关闭所有连接
            for conn in connections:
                conn.close()
            return None
    
    @staticmethod
    def execute_many(sql, params_list, db_name='stock.db'):
        """
        在所有数据库路径上执行批量SQL语句
        
        Args:
            sql: SQL语句
            params_list: SQL参数列表
            db_name: 数据库文件名
        """
        db_paths = DbUtils.get_db_paths(db_name)
        connections = []
        
        for db_path in db_paths:
            try:
                # 确保数据库目录存在
                db_dir = os.path.dirname(db_path)
                if not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                
                # 连接数据库
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 执行批量SQL
                cursor.executemany(sql, params_list)
                
                # 提交事务
                conn.commit()
                
                # 关闭连接
                cursor.close()
                conn.close()
            except Exception as e:
                print(f"执行批量SQL出错 ({db_path}): {e}")
                # 关闭之前成功的连接
                for conn in connections:
                    try:
                        conn.close()
                    except:
                        pass
    
    @staticmethod
    def query(sql, params=None, db_name='stock.db', fetch_all=True):
        """
        在第一个可用的数据库路径上执行查询
        
        Args:
            sql: SQL查询语句
            params: SQL参数
            db_name: 数据库文件名
            fetch_all: 是否获取所有结果
            
        Returns:
            查询结果
        """
        db_paths = DbUtils.get_db_paths(db_name)
        
        for db_path in db_paths:
            if os.path.exists(db_path):
                try:
                    # 连接数据库
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 执行查询
                    if params:
                        cursor.execute(sql, params)
                    else:
                        cursor.execute(sql)
                    
                    # 获取结果
                    if fetch_all:
                        result = cursor.fetchall()
                    else:
                        result = cursor.fetchone()
                    
                    # 关闭连接
                    cursor.close()
                    conn.close()
                    
                    return result
                except Exception as e:
                    print(f"执行查询出错 ({db_path}): {e}")
        
        # 如果所有数据库路径都不可用
        return [] if fetch_all else None
    
    @staticmethod
    def create_table_if_not_exists(table_name, columns_definition, db_name='stock.db'):
        """
        在所有数据库路径上创建表（如果不存在）
        
        Args:
            table_name: 表名
            columns_definition: 列定义字符串
            db_name: 数据库文件名
        """
        sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_definition})"
        DbUtils.execute_sql(sql, db_name=db_name)
    
    @staticmethod
    def table_exists(table_name, db_name='stock.db'):
        """
        检查表是否存在于第一个可用的数据库中
        
        Args:
            table_name: 表名
            db_name: 数据库文件名
            
        Returns:
            表是否存在
        """
        sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = DbUtils.query(sql, (table_name,), db_name)
        return len(result) > 0
    
    @staticmethod
    def sync_databases(db_name='stock.db'):
        """
        同步两个位置的数据库，确保它们内容一致
        使用修改时间较新的数据库覆盖较旧的
        
        Args:
            db_name: 数据库文件名
        """
        db_paths = DbUtils.get_db_paths(db_name)
        
        # 检查哪些数据库存在
        existing_dbs = [path for path in db_paths if os.path.exists(path)]
        
        if len(existing_dbs) > 1:
            # 获取各数据库的修改时间
            db_times = [(path, os.path.getmtime(path)) for path in existing_dbs]
            
            # 按修改时间排序
            db_times.sort(key=lambda x: x[1], reverse=True)
            
            # 最新的数据库
            newest_db = db_times[0][0]
            
            # 用最新的数据库覆盖其他数据库
            for path in existing_dbs:
                if path != newest_db:
                    try:
                        # 备份旧数据库
                        backup_path = f"{path}.bak.{int(time.time())}"
                        shutil.copy2(path, backup_path)
                        
                        # 复制新数据库
                        shutil.copy2(newest_db, path)
                        
                        print(f"已同步数据库: {newest_db} -> {path}")
                    except Exception as e:
                        print(f"同步数据库出错: {e}")
        elif len(existing_dbs) == 1:
            # 只有一个数据库存在，复制到另一个位置
            existing_db = existing_dbs[0]
            missing_dbs = [path for path in db_paths if path not in existing_dbs]
            
            for path in missing_dbs:
                try:
                    # 确保目录存在
                    db_dir = os.path.dirname(path)
                    if not os.path.exists(db_dir):
                        os.makedirs(db_dir, exist_ok=True)
                    
                    # 复制数据库
                    shutil.copy2(existing_db, path)
                    
                    print(f"已复制数据库: {existing_db} -> {path}")
                except Exception as e:
                    print(f"复制数据库出错: {e}")


# 测试代码
if __name__ == "__main__":
    # 获取数据库路径
    db_paths = DbUtils.get_db_paths()
    print("数据库路径:", db_paths)
    
    # 创建测试表
    DbUtils.create_table_if_not_exists(
        "test_table",
        "id INTEGER PRIMARY KEY, name TEXT, value REAL, timestamp TEXT"
    )
    
    # 插入测试数据
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    DbUtils.execute_sql(
        "INSERT INTO test_table (name, value, timestamp) VALUES (?, ?, ?)",
        ("测试数据", 123.45, timestamp)
    )
    
    # 查询数据
    results = DbUtils.query("SELECT * FROM test_table")
    print("查询结果:", results)
    
    # 同步数据库
    DbUtils.sync_databases()
