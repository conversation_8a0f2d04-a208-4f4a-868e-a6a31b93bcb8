import os
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinter.font import Font

class ExportSWIndustryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("申万行业分类导出工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 12))
        self.style.configure("TLabel", font=("Arial", 12))
        self.style.configure("TFrame", background="#f0f0f0")
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.Frame(main_frame, padding="5")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="Excel文件:").pack(side=tk.LEFT, padx=5)
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        browse_button = ttk.Button(file_frame, text="浏览...", command=self.browse_file)
        browse_button.pack(side=tk.LEFT, padx=5)
        
        # 输出文件区域
        output_frame = ttk.Frame(main_frame, padding="5")
        output_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(output_frame, text="输出文件:").pack(side=tk.LEFT, padx=5)
        
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        save_button = ttk.Button(output_frame, text="浏览...", command=self.browse_output)
        save_button.pack(side=tk.LEFT, padx=5)
        
        # 列选择区域
        columns_frame = ttk.Frame(main_frame, padding="5")
        columns_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(columns_frame, text="股票代码列:").pack(side=tk.LEFT, padx=5)
        self.code_column_var = tk.StringVar(value="代码")
        code_entry = ttk.Entry(columns_frame, textvariable=self.code_column_var, width=10)
        code_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(columns_frame, text="申万行业列:").pack(side=tk.LEFT, padx=5)
        self.industry_column_var = tk.StringVar(value="申万三级")
        industry_entry = ttk.Entry(columns_frame, textvariable=self.industry_column_var, width=10)
        industry_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(columns_frame, text="市场列:").pack(side=tk.LEFT, padx=5)
        self.market_column_var = tk.StringVar(value="市场")
        market_entry = ttk.Entry(columns_frame, textvariable=self.market_column_var, width=10)
        market_entry.pack(side=tk.LEFT, padx=5)
        
        # 处理按钮
        button_frame = ttk.Frame(main_frame, padding="5")
        button_frame.pack(fill=tk.X, pady=10)
        
        process_button = ttk.Button(button_frame, text="导出申万行业", command=self.process_file)
        process_button.pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        progress_frame = ttk.Frame(main_frame, padding="5")
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5)
        
        # 日志区域
        log_frame = ttk.Frame(main_frame, padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        log_label = ttk.Label(log_frame, text="处理日志:")
        log_label.pack(anchor=tk.W, padx=5)
        
        self.log_text = tk.Text(log_frame, height=15, width=80, font=("Courier", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 初始化日志
        self.log("申万行业分类导出工具已启动")
        self.log("请选择Excel文件并设置输出路径")
    
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.log(f"已选择文件: {file_path}")
            
            # 自动设置输出文件路径
            dir_name = os.path.dirname(file_path)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_path = os.path.join(dir_name, f"{base_name}_申万行业.txt")
            self.output_path_var.set(output_path)
            self.log(f"自动设置输出路径: {output_path}")
    
    def browse_output(self):
        output_path = filedialog.asksaveasfilename(
            title="保存输出文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")]
        )
        if output_path:
            self.output_path_var.set(output_path)
            self.log(f"已设置输出路径: {output_path}")
    
    def log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def process_file(self):
        # 获取输入参数
        file_path = self.file_path_var.get()
        output_path = self.output_path_var.get()
        code_column = self.code_column_var.get()
        industry_column = self.industry_column_var.get()
        market_column = self.market_column_var.get()
        
        # 验证输入
        if not file_path:
            messagebox.showerror("错误", "请选择Excel文件")
            return
        
        if not output_path:
            messagebox.showerror("错误", "请设置输出文件路径")
            return
        
        try:
            # 读取Excel文件
            self.log(f"正在读取Excel文件: {file_path}")
            self.progress_var.set(10)
            
            df = pd.read_excel(file_path)
            self.log(f"成功读取Excel文件，共 {len(df)} 行数据")
            self.progress_var.set(30)
            
            # 检查列是否存在
            required_columns = [code_column, industry_column]
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.log(f"错误: Excel文件中缺少以下列: {', '.join(missing_columns)}")
                self.log(f"可用列: {', '.join(df.columns)}")
                messagebox.showerror("错误", f"Excel文件中缺少以下列: {', '.join(missing_columns)}")
                return
            
            # 处理市场列
            market_map = {"深圳": "0", "上海": "1", "北交所": "2"}
            
            if market_column in df.columns:
                self.log(f"使用 '{market_column}' 列作为市场标识")
                # 尝试映射市场名称到数字代码
                df['market_code'] = df[market_column].apply(
                    lambda x: market_map.get(str(x).strip(), "2")  # 默认为北交所
                )
            else:
                self.log(f"未找到市场列 '{market_column}'，将根据股票代码自动判断市场")
                # 根据股票代码判断市场
                df['market_code'] = df[code_column].apply(
                    lambda x: "0" if str(x).startswith(("000", "001", "002", "003", "300", "301")) else 
                             "1" if str(x).startswith(("600", "601", "603", "605", "688", "689")) else "2"
                )
            
            self.progress_var.set(50)
            
            # 提取股票代码（确保是6位数字）
            df['clean_code'] = df[code_column].apply(
                lambda x: ''.join(filter(str.isdigit, str(x)))[-6:].zfill(6)
            )
            
            # 准备输出数据
            output_data = []
            total_rows = len(df)
            
            self.log("正在处理数据...")
            
            for i, row in df.iterrows():
                # 更新进度条
                progress = 50 + (i / total_rows) * 40
                self.progress_var.set(progress)
                
                if i % 100 == 0:
                    self.root.update_idletasks()
                
                market_code = row['market_code']
                stock_code = row['clean_code']
                industry = str(row[industry_column]) if pd.notna(row[industry_column]) else ""
                
                # 格式化为指定格式
                output_line = f"{market_code}|{stock_code}|{industry}|0.000"
                output_data.append(output_line)
            
            self.progress_var.set(90)
            
            # 写入输出文件
            self.log(f"正在写入输出文件: {output_path}")
            
            with open(output_path, 'w', encoding='utf-8') as f:
                for line in output_data:
                    f.write(line + '\n')
            
            self.progress_var.set(100)
            self.log(f"处理完成，共导出 {len(output_data)} 条记录")
            messagebox.showinfo("成功", f"申万行业分类已成功导出到: {output_path}")
            
        except Exception as e:
            self.log(f"处理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"处理失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ExportSWIndustryApp(root)
    root.mainloop()
