import os
import configparser
import sqlite3
import pandas as pd
import tushare as ts
import time
import threading
import concurrent.futures
from PyQt5.QtCore import pyqtSignal, QObject, Qt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QProgressBar, QMessageBox, QGroupBox, QTextEdit, QLineEdit)

class StockUpdaterSignals(QObject):
    """
    定义股票更新器的信号
    """
    update_log = pyqtSignal(str)
    update_progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)

class StockUpdater:
    """股票更新类，用于通过Tushare API更新数据库中的股票信息"""

    def __init__(self, config_path='config.ini'):
        """初始化股票更新器"""
        self.config_path = config_path
        self.token = self._get_token()
        self.pro = None
        if self.token:
            ts.set_token(self.token)
            self.pro = ts.pro_api()
        self.signals = StockUpdaterSignals()

    def _get_token(self):
        """从配置文件中获取Tushare API token"""
        if not os.path.exists(self.config_path):
            self.signals.error.emit(f"配置文件不存在: {self.config_path}")
            return None

        try:
            config = configparser.ConfigParser()
            config.read(self.config_path, encoding='utf-8')
            token = config.get('API', 'token')
            return token
        except Exception as e:
            self.signals.error.emit(f"读取配置文件出错: {str(e)}")
            return None

    def update_stock_names(self, db_path='stock.db', table_name='stocks', update_industry=False, update_area=False):
        """更新数据库中的股票名称

        Args:
            db_path: 数据库文件路径
            table_name: 表名
            update_industry: 是否同时更新行业信息，默认为False
            update_area: 是否同时更新地区信息，默认为False
        """
        if not self.pro:
            self.signals.error.emit("Tushare API未初始化，请检查token是否有效")
            return False

        try:
            self.signals.update_log.emit("开始更新股票名称...")
            self.signals.update_progress.emit(10)

            # 检查数据库文件是否存在
            if not os.path.exists(db_path):
                self.signals.error.emit(f"数据库文件不存在: {db_path}")
                return False

            # 连接数据库
            self.signals.update_log.emit("正在连接数据库...")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                conn.close()
                self.signals.error.emit(f"数据表不存在: {table_name}")
                return False

            # 获取数据库中的股票代码
            self.signals.update_log.emit("正在获取数据库中的股票代码...")
            self.signals.update_progress.emit(20)
            cursor.execute(f"SELECT 代码 FROM [{table_name}]")
            db_codes = cursor.fetchall()

            if not db_codes:
                conn.close()
                self.signals.error.emit("数据库中没有股票代码数据")
                return False

            # 提取代码列表
            db_codes = [code[0] for code in db_codes]
            self.signals.update_log.emit(f"数据库中共有 {len(db_codes)} 个股票代码")

            # 从Tushare获取股票基本信息
            self.signals.update_log.emit("正在从Tushare获取股票基本信息...")
            self.signals.update_progress.emit(30)

            # 获取所有股票列表
            stock_basic = self.pro.stock_basic(exchange='', list_status='L',
                                              fields='ts_code,symbol,name,area,industry,list_date')

            if stock_basic.empty:
                conn.close()
                self.signals.error.emit("从Tushare获取股票信息失败")
                return False

            self.signals.update_log.emit(f"从Tushare获取了 {len(stock_basic)} 个股票的基本信息")
            self.signals.update_progress.emit(50)

            # 创建代码到名称的映射
            code_to_name = {}
            code_to_area = {} if update_area else None
            code_to_industry = {} if update_industry else None

            for _, row in stock_basic.iterrows():
                ts_code = row['ts_code']
                symbol = row['symbol']
                # 处理不同格式的代码
                if '.' in ts_code:
                    # 转换Tushare格式的代码（如：000001.SZ）为数据库格式
                    code = ts_code.split('.')[0]
                    # 同时保存原始格式的代码
                    code_with_suffix = ts_code
                else:
                    code = ts_code
                    # 如果没有后缀，尝试添加后缀
                    if code.startswith('6'):
                        code_with_suffix = f"{code}.SH"
                    else:
                        code_with_suffix = f"{code}.SZ"

                # 保存多种可能的代码格式
                code_to_name[code] = row['name']  # 纯数字代码
                code_to_name[code_with_suffix] = row['name']  # 带后缀的代码

                # 添加带前缀的格式
                if code_with_suffix.endswith('.SH'):
                    code_to_name[f"SH{code}"] = row['name']
                elif code_with_suffix.endswith('.SZ'):
                    code_to_name[f"SZ{code}"] = row['name']

                # 保存地区信息（仅当update_area为True时）
                if update_area and 'area' in row:
                    code_to_area[code] = row['area']
                    code_to_area[code_with_suffix] = row['area']
                    if code_with_suffix.endswith('.SH'):
                        code_to_area[f"SH{code}"] = row['area']
                    elif code_with_suffix.endswith('.SZ'):
                        code_to_area[f"SZ{code}"] = row['area']

                # 保存行业信息（仅当update_industry为True时）
                if update_industry and 'industry' in row:
                    code_to_industry[code] = row['industry']
                    code_to_industry[code_with_suffix] = row['industry']
                    if code_with_suffix.endswith('.SH'):
                        code_to_industry[f"SH{code}"] = row['industry']
                    elif code_with_suffix.endswith('.SZ'):
                        code_to_industry[f"SZ{code}"] = row['industry']

            # 更新数据库中的股票名称
            self.signals.update_log.emit("正在更新数据库中的股票名称...")
            self.signals.update_progress.emit(70)

            # 检查数据库表是否有名称列
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns = [column[1] for column in cursor.fetchall()]

            # 关闭数据库连接，让后续操作使用自己的连接
            conn.close()

            # 从config.ini读取批量大小和线程数
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            batch_size = int(config.get('Settings', 'batch_size', fallback='500'))

            # 确保批量大小不超过API限制
            if batch_size > 500:
                self.signals.update_log.emit(f"警告: 配置的批量大小 {batch_size} 可能超出API限制，调整为 500")
                batch_size = 500

            # 修改为单线程处理，避免数据库锁定问题
            thread_count = 1  # 强制使用单线程

            self.signals.update_log.emit(f"批量大小: {batch_size}, 线程数: {thread_count}")

            # 分批处理
            batches = [db_codes[i:i+batch_size] for i in range(0, len(db_codes), batch_size)]
            total_batches = len(batches)
            self.signals.update_log.emit(f"分为 {total_batches} 批处理")

            # 单线程顺序处理所有批次
            total_updated = 0
            total_not_found = 0
            all_not_found_codes = []

            for i, batch in enumerate(batches):
                batch_num = i + 1
                self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 开始处理...")

                # 处理当前批次
                result = self._update_names_batch(
                    batch,
                    batch_num,
                    total_batches,
                    db_path,
                    table_name,
                    code_to_name,
                    code_to_area if update_area else None,
                    code_to_industry if update_industry else None,
                    columns
                )

                # 获取批次处理结果
                batch_num, updated_count, not_found_count, not_found_codes = result
                total_updated += updated_count
                total_not_found += not_found_count
                all_not_found_codes.extend(not_found_codes)

                # 更新进度
                progress = int((batch_num / total_batches) * 100)
                self.signals.update_progress.emit(progress)
                self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 完成: 更新 {updated_count}, 未找到 {not_found_count}")

            self.signals.update_progress.emit(100)
            self.signals.update_log.emit(f"更新完成！共更新 {total_updated} 个股票名称")

            if total_not_found > 0:
                self.signals.update_log.emit(f"有 {total_not_found} 个代码在Tushare中未找到")
                if len(all_not_found_codes) <= 10:
                    self.signals.update_log.emit(f"未找到的代码: {', '.join(all_not_found_codes)}")
                else:
                    self.signals.update_log.emit(f"部分未找到的代码: {', '.join(all_not_found_codes[:10])}...")

            self.signals.finished.emit()
            return True

        except Exception as e:
            import traceback
            error_msg = f"更新股票名称时出错: {str(e)}\n{traceback.format_exc()}"
            self.signals.error.emit(error_msg)
            return False

    def update_stock_prices_and_market_cap(self, db_path='stock.db', table_name='stocks', test_code=None):
        """更新数据库中的股票最新价格和自由流通市值

        Args:
            db_path: 数据库文件路径
            table_name: 数据表名称
            test_code: 测试用的股票代码，如果提供则只更新该股票
        """
        if not self.pro:
            self.signals.error.emit("Tushare API未初始化，请检查token是否有效")
            return False

        # 初始化失败股票记录字典
        db_failed_stocks = {}

        try:
            self.signals.update_log.emit("开始更新股票价格和自由流通市值...")
            self.signals.update_progress.emit(10)

            # 检查数据库文件是否存在
            if not os.path.exists(db_path):
                self.signals.error.emit(f"数据库文件不存在: {db_path}")
                return False

            # 连接数据库
            self.signals.update_log.emit("正在连接数据库...")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                conn.close()
                self.signals.error.emit(f"数据表不存在: {table_name}")
                return False

            # 获取数据库中的股票代码
            self.signals.update_log.emit("正在获取数据库中的股票代码...")
            self.signals.update_progress.emit(20)

            if test_code:
                # 如果提供了测试代码，只更新该股票
                self.signals.update_log.emit(f"测试模式：只更新股票代码 {test_code}")

                # 尝试多种可能的代码格式
                possible_codes = [
                    test_code,  # 原始代码
                ]

                # 根据代码特征添加可能的格式
                if test_code.isdigit():
                    # 纯数字代码
                    if test_code.startswith('688'):
                        # 科创板股票
                        possible_codes.append(f"{test_code}.SH")
                        possible_codes.append(f"SH{test_code}")
                    elif test_code.startswith('6'):
                        # 上海主板股票
                        possible_codes.append(f"{test_code}.SH")
                        possible_codes.append(f"SH{test_code}")
                    elif test_code.startswith('3'):
                        # 创业板股票
                        possible_codes.append(f"{test_code}.SZ")
                        possible_codes.append(f"SZ{test_code}")
                    elif test_code.startswith('00'):
                        # 深圳主板股票
                        possible_codes.append(f"{test_code}.SZ")
                        possible_codes.append(f"SZ{test_code}")
                    elif test_code.startswith('4') or test_code.startswith('8') or test_code.startswith('9'):
                        # 北交所股票
                        possible_codes.append(f"{test_code}.BJ")
                        possible_codes.append(f"BJ{test_code}")
                    else:
                        # 其他股票，默认尝试深圳
                        possible_codes.append(f"{test_code}.SZ")
                        possible_codes.append(f"SZ{test_code}")
                elif test_code.upper().endswith('.SH') or test_code.upper().endswith('.SZ') or test_code.upper().endswith('.BJ'):
                    # 带后缀的代码
                    parts = test_code.split('.')
                    possible_codes.append(parts[0])  # 纯数字部分
                    possible_codes.append(f"{parts[1]}{parts[0]}")  # 前缀格式
                elif (test_code.upper().startswith('SH') or test_code.upper().startswith('SZ') or test_code.upper().startswith('BJ')) and test_code[2:].isdigit():
                    # 带前缀的代码
                    prefix = test_code[:2].upper()
                    number_part = test_code[2:]
                    possible_codes.append(number_part)  # 纯数字部分
                    possible_codes.append(f"{number_part}.{prefix}")  # 后缀格式

                # 特殊处理：如果是纯数字代码，同时添加所有可能的交易所后缀
                if test_code.isdigit():
                    # 添加所有可能的后缀格式，确保覆盖所有交易所
                    if f"{test_code}.SH" not in possible_codes:
                        possible_codes.append(f"{test_code}.SH")
                    if f"{test_code}.SZ" not in possible_codes:
                        possible_codes.append(f"{test_code}.SZ")
                    if f"{test_code}.BJ" not in possible_codes:
                        possible_codes.append(f"{test_code}.BJ")

                # 去除重复并过滤掉无效的代码
                possible_codes = list(set([code for code in possible_codes if code != test_code]))
                possible_codes.insert(0, test_code)  # 确保原始代码在最前面

                self.signals.update_log.emit(f"尝试查找的代码格式: {', '.join(possible_codes)}")

                # 构建SQL查询
                placeholders = ', '.join(['?'] * len(possible_codes))
                cursor.execute(f"SELECT 代码 FROM [{table_name}] WHERE 代码 IN ({placeholders})", possible_codes)
                db_codes = cursor.fetchall()

                if db_codes:
                    # 使用找到的第一个代码
                    found_code = db_codes[0][0]
                    self.signals.update_log.emit(f"在数据库中找到匹配的代码: {found_code}")
                    db_codes = [(found_code,)]
                    # 更新测试代码为找到的代码
                    test_code = found_code
                else:
                    self.signals.update_log.emit(f"警告：数据库中未找到匹配的股票代码，尝试直接使用 {test_code}")
                    db_codes = [(test_code,)]
            else:
                # 否则更新所有股票
                cursor.execute(f"SELECT 代码 FROM [{table_name}]")
                db_codes = cursor.fetchall()

            if not db_codes:
                conn.close()
                self.signals.error.emit("数据库中没有股票代码数据")
                return False

            # 提取代码列表
            db_codes = [code[0] for code in db_codes]
            self.signals.update_log.emit(f"将要更新 {len(db_codes)} 个股票代码")

            # 检查数据库表是否有需要的列
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns = [column[1] for column in cursor.fetchall()]

            # 确保表中有必要的列
            required_columns = ['股价', '流通股', '市值Z', '名称']
            missing_columns = []

            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)

            if missing_columns:
                # 添加缺失的列
                self.signals.update_log.emit(f"正在添加缺失的列: {', '.join(missing_columns)}")
                for col in missing_columns:
                    cursor.execute(f"ALTER TABLE [{table_name}] ADD COLUMN [{col}] REAL")
                conn.commit()

            # 使用更简单的方法：直接获取股票行情数据
            self.signals.update_log.emit("正在从Tushare获取股票行情数据...")
            self.signals.update_progress.emit(30)

            # 处理测试代码
            if test_code:
                # 尝试转换为Tushare格式的代码
                ts_code = None
                original_code = test_code.strip()

                # 如果是带后缀的代码（如600001.SH, 000001.SZ, 430047.BJ）
                if original_code.upper().endswith('.SH') or original_code.upper().endswith('.SZ') or original_code.upper().endswith('.BJ'):
                    ts_code = original_code.upper()

                # 如果是带前缀的代码（如SH600001, SZ000001, BJ430047）
                elif (original_code.upper().startswith('SH') or original_code.upper().startswith('SZ') or original_code.upper().startswith('BJ')) and original_code[2:].isdigit():
                    prefix = original_code[:2].upper()
                    number_part = original_code[2:]
                    ts_code = f"{number_part}.{prefix}"

                # 如果是纯数字代码（如600001, 000001, 430047, 920060）
                elif original_code.isdigit():
                    if original_code.startswith('688'):
                        # 科创板股票
                        ts_code = f"{original_code}.SH"
                    elif original_code.startswith('6'):
                        # 上海主板股票
                        ts_code = f"{original_code}.SH"
                    elif original_code.startswith('3'):
                        # 创业板股票
                        ts_code = f"{original_code}.SZ"
                    elif original_code.startswith('00'):
                        # 深圳主板股票
                        ts_code = f"{original_code}.SZ"
                    elif original_code.startswith('4') or original_code.startswith('8') or original_code.startswith('9'):
                        # 北交所股票
                        ts_code = f"{original_code}.BJ"
                    else:
                        # 其他股票，默认尝试深圳
                        ts_code = f"{original_code}.SZ"

                if not ts_code:
                    conn.close()
                    self.signals.error.emit(f"无法识别的股票代码格式: {test_code}")
                    return False

                self.signals.update_log.emit(f"转换后的Tushare代码: {ts_code}")

                # 获取更新前的数据（如果存在）
                old_data = {}
                try:
                    # 检查表中是否有通达信行业列
                    cursor.execute(f"PRAGMA table_info([{table_name}])")
                    columns = [column[1] for column in cursor.fetchall()]

                    if '通达信行业' in columns:
                        cursor.execute(f"SELECT 名称, 股价, 市值Z, 流通股, 通达信行业 FROM [{table_name}] WHERE 代码 = ?", (test_code,))
                        result = cursor.fetchone()
                        if result:
                            old_data = {
                                '名称': result[0] if result[0] is not None else '未知',
                                '股价': result[1] if result[1] is not None else 0,
                                '市值Z': result[2] if result[2] is not None else 0,
                                '流通股': result[3] if result[3] is not None else 0,
                                '通达信行业': result[4] if result[4] is not None else '未知'
                            }
                            self.signals.update_log.emit(f"更新前数据: 名称={old_data['名称']}, 股价={old_data['股价']}, 市值Z={old_data['市值Z']}, 流通股={old_data['流通股']}, 通达信行业={old_data['通达信行业']}")
                        else:
                            self.signals.update_log.emit("数据库中没有该股票的历史数据")
                    else:
                        cursor.execute(f"SELECT 名称, 股价, 市值Z, 流通股 FROM [{table_name}] WHERE 代码 = ?", (test_code,))
                        result = cursor.fetchone()
                        if result:
                            old_data = {
                                '名称': result[0] if result[0] is not None else '未知',
                                '股价': result[1] if result[1] is not None else 0,
                                '市值Z': result[2] if result[2] is not None else 0,
                                '流通股': result[3] if result[3] is not None else 0,
                                '通达信行业': '未知'
                            }
                            self.signals.update_log.emit(f"更新前数据: 名称={old_data['名称']}, 股价={old_data['股价']}, 市值Z={old_data['市值Z']}, 流通股={old_data['流通股']}, 通达信行业=未知")
                        else:
                            self.signals.update_log.emit("数据库中没有该股票的历史数据")
                except Exception as e:
                    self.signals.update_log.emit(f"获取历史数据时出错: {str(e)}")

                # 获取单只股票的基本信息
                try:
                    # 获取股票基本信息
                    stock_info = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,name,list_date')
                    if stock_info.empty:
                        conn.close()
                        self.signals.error.emit(f"未找到股票 {ts_code} 的基本信息")
                        return False

                    name = stock_info.iloc[0]['name']
                    self.signals.update_log.emit(f"获取到股票名称: {name}")

                    # 获取最新行情
                    today = time.strftime('%Y%m%d')
                    df = self.pro.daily(ts_code=ts_code, start_date='20230101', end_date=today)
                    if df.empty:
                        conn.close()
                        self.signals.error.emit(f"未找到股票 {ts_code} 的行情数据")
                        return False

                    # 获取最新一天的收盘价，保留两位小数，但作为字符串存储以匹配数据库TEXT类型
                    latest_price = str(round(float(df.iloc[0]['close']), 2))
                    self.signals.update_log.emit(f"获取到最新收盘价: {latest_price}")

                    # 获取流通股和自由流通股数据
                    try:
                        # 先尝试获取自由流通股数据
                        daily_basic_data = self.pro.daily_basic(ts_code=ts_code, fields='ts_code,free_share,circ_mv')
                        if not daily_basic_data.empty and 'free_share' in daily_basic_data.columns and pd.notna(daily_basic_data.iloc[0]['free_share']):
                            # 使用daily_basic接口获取的自由流通股数据
                            free_share = daily_basic_data.iloc[0]['free_share'] / 10000  # 转换为亿股
                            self.signals.update_log.emit(f"获取到自由流通股(daily_basic): {free_share:.2f}亿股")

                            # 如果有直接的自由流通市值数据，也记录下来
                            if 'circ_mv' in daily_basic_data.columns and pd.notna(daily_basic_data.iloc[0]['circ_mv']):
                                circ_mv = daily_basic_data.iloc[0]['circ_mv'] / 10000  # 转换为亿元
                                self.signals.update_log.emit(f"获取到自由流通市值(daily_basic): {circ_mv:.2f}亿元")
                        else:
                            # 如果daily_basic接口没有数据，尝试其他方法
                            self.signals.update_log.emit("未从daily_basic获取到自由流通股数据，尝试其他方法...")

                            # 获取总流通股数据
                            float_data = self.pro.bak_basic(ts_code=ts_code, fields='ts_code,float_share')
                            if float_data.empty:
                                conn.close()
                                self.signals.error.emit(f"未找到股票 {ts_code} 的流通股数据")
                                return False

                            # 总流通股
                            float_share = float_data.iloc[0]['float_share']  # 单位：亿股
                            self.signals.update_log.emit(f"获取到总流通股: {float_share:.2f}亿股")

                            # 估算自由流通股（通常为总流通股的一部分，这里假设为30%）
                            free_share = float_share * 0.3
                            self.signals.update_log.emit(f"估算自由流通股(总流通股的30%): {free_share:.2f}亿股")
                    except Exception as e:
                        self.signals.update_log.emit(f"获取自由流通股数据时出错: {str(e)}，使用备用方法...")
                        # 如果出错，尝试从行情数据中获取总市值，然后估算
                        try:
                            quote_data = self.pro.daily_basic(ts_code=ts_code, fields='ts_code,total_mv,circ_mv')
                            if not quote_data.empty:
                                if 'circ_mv' in quote_data.columns and pd.notna(quote_data.iloc[0]['circ_mv']):
                                    circ_mv = quote_data.iloc[0]['circ_mv'] / 10000  # 转换为亿元
                                    self.signals.update_log.emit(f"获取到流通市值: {circ_mv:.2f}亿元")
                                    # 估算自由流通股
                                    free_share = circ_mv / latest_price
                                    self.signals.update_log.emit(f"根据流通市值估算自由流通股: {free_share:.2f}亿股")
                                else:
                                    # 如果没有流通市值数据，使用默认值
                                    free_share = 1.0  # 默认1亿股
                                    self.signals.update_log.emit(f"无法获取准确数据，使用默认自由流通股: {free_share}亿股")
                            else:
                                # 如果没有数据，使用默认值
                                free_share = 1.0  # 默认1亿股
                                self.signals.update_log.emit(f"无法获取准确数据，使用默认自由流通股: {free_share}亿股")
                        except:
                            # 如果再次出错，使用默认值
                            free_share = 1.0  # 默认1亿股
                            self.signals.update_log.emit(f"无法获取准确数据，使用默认自由流通股: {free_share}亿股")

                    # 计算自由流通市值（单位：亿元），保留两位小数
                    market_cap = round(float(latest_price) * float(free_share), 2)
                    self.signals.update_log.emit(f"计算得到自由流通市值: {market_cap}亿元")

                    # 更新数据库 - 确保股价作为TEXT类型处理，同时更新名称和自由流通股
                    cursor.execute(f"""
                        UPDATE [{table_name}]
                        SET 股价 = ?, 流通股 = ?, 市值Z = ?, 名称 = ?
                        WHERE 代码 = ?
                    """, (str(latest_price), free_share, market_cap, name, test_code))

                    # 提交事务，但不关闭连接，因为后面还需要更新通达信行业
                    conn.commit()

                    self.signals.update_progress.emit(100)

                    # 显示更新前后的对比
                    if old_data:
                        # 确保所有值都是浮点数类型，但注意股价在数据库中是TEXT类型
                        try:
                            # 将字符串类型的股价转换为浮点数进行计算
                            old_price = float(old_data['股价']) if old_data['股价'] is not None else 0.0
                            old_market_cap = float(old_data['市值Z']) if old_data['市值Z'] is not None else 0.0
                            old_free_share = float(old_data['流通股']) if old_data['流通股'] is not None else 0.0

                            # 计算变化值和百分比 - 将字符串类型的latest_price转换为浮点数
                            latest_price_float = float(latest_price)
                            price_change = latest_price_float - old_price
                            price_change_pct = (price_change / old_price * 100) if old_price != 0 else 0.0

                            # 保存测试结果到日志文件
                            try:
                                log_dir = "logs"
                                if not os.path.exists(log_dir):
                                    os.makedirs(log_dir)

                                log_file = os.path.join(log_dir, f"test_{test_code}_{time.strftime('%Y%m%d_%H%M%S')}.log")
                                self.signals.update_log.emit(f"正在保存测试结果到: {log_file}")

                                with open(log_file, "w", encoding="utf-8") as f:
                                    f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                                    f.write(f"股票代码: {test_code}\n")
                                    f.write(f"Tushare代码: {ts_code}\n")
                                    f.write(f"股票名称: {name}\n\n")

                                    f.write("更新前数据:\n")
                                    f.write("-" * 30 + "\n")
                                    f.write(f"股价: {old_price:.2f}\n")
                                    f.write(f"流通股: {old_free_share:.2f} 亿股\n")
                                    f.write(f"市值: {old_market_cap:.2f} 亿元\n\n")

                                    f.write("更新后数据:\n")
                                    f.write("-" * 30 + "\n")
                                    f.write(f"股价: {float(latest_price):.2f}\n")
                                    f.write(f"流通股: {free_share:.2f} 亿股\n")
                                    f.write(f"市值: {market_cap:.2f} 亿元\n\n")

                                    f.write("变化情况:\n")
                                    f.write("-" * 30 + "\n")
                                    f.write(f"股价变化: {price_change:.2f} ({price_change_pct:.2f}%)\n")

                                    market_cap_change = market_cap - old_market_cap
                                    market_cap_change_pct = (market_cap_change / old_market_cap * 100) if old_market_cap != 0 else 0.0
                                    f.write(f"市值变化: {market_cap_change:.2f} 亿元 ({market_cap_change_pct:.2f}%)\n")

                                    free_share_change = free_share - old_free_share
                                    free_share_change_pct = (free_share_change / old_free_share * 100) if old_free_share != 0 else 0.0
                                    f.write(f"流通股变化: {free_share_change:.2f} 亿股 ({free_share_change_pct:.2f}%)\n")

                                self.signals.update_log.emit(f"测试结果已保存到: {log_file}")
                            except Exception as e:
                                self.signals.update_log.emit(f"保存测试结果时出错: {str(e)}")

                            market_cap_change = float(market_cap) - old_market_cap
                            market_cap_change_pct = (market_cap_change / old_market_cap * 100) if old_market_cap != 0 else 0.0

                            free_share_change = float(free_share) - old_free_share
                            free_share_change_pct = (free_share_change / old_free_share * 100) if old_free_share != 0 else 0.0
                        except Exception as e:
                            self.signals.update_log.emit(f"计算变化值时出错: {str(e)}，使用默认值")
                            # 如果计算出错，使用默认值
                            price_change = 0.0
                            price_change_pct = 0.0
                            market_cap_change = 0.0
                            market_cap_change_pct = 0.0
                            free_share_change = 0.0
                            free_share_change_pct = 0.0

                        # 名称变化
                        name_changed = "（已变更）" if old_data['名称'] != name and old_data['名称'] != '未知' else ""

                        # 显示对比信息
                        self.signals.update_log.emit("\n===== 更新前后对比 =====")
                        self.signals.update_log.emit(f"名称: {old_data['名称']} -> {name} {name_changed}")
                        # 格式化显示，保留两位小数，注意股价是字符串类型
                        formatted_old_price = round(float(old_price), 2)
                        formatted_latest_price = round(float(latest_price), 2)  # 将字符串转为浮点数再格式化
                        formatted_old_free_share = round(float(old_free_share), 2)
                        formatted_free_share = round(float(free_share), 2)
                        formatted_old_market_cap = round(float(old_market_cap), 2)
                        formatted_market_cap = round(float(market_cap), 2)

                        self.signals.update_log.emit(f"股价: {formatted_old_price} -> {formatted_latest_price} [变化: {price_change:+.2f} ({price_change_pct:+.2f}%)]")
                        self.signals.update_log.emit(f"流通股: {formatted_old_free_share}亿股 -> {formatted_free_share}亿股 [变化: {free_share_change:+.2f} ({free_share_change_pct:+.2f}%)]")
                        self.signals.update_log.emit(f"市值Z: {formatted_old_market_cap}亿元 -> {formatted_market_cap}亿元 [变化: {market_cap_change:+.2f} ({market_cap_change_pct:+.2f}%)]")

                        # 获取通达信行业信息
                        try:
                            # 获取更新前的通达信行业
                            old_industry = old_data.get('通达信行业', '未知')

                            # 获取最新的通达信行业
                            self.signals.update_log.emit(f"正在获取通达信行业信息，使用代码: {ts_code}")
                            industry_info = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,industry')
                            new_industry = '未知'

                            if not industry_info.empty and 'industry' in industry_info.columns and pd.notna(industry_info.iloc[0]['industry']):
                                new_industry = industry_info.iloc[0]['industry']
                                self.signals.update_log.emit(f"成功获取到通达信行业: {new_industry}")

                                # 更新数据库中的通达信行业
                                cursor.execute("UPDATE [stocks] SET 通达信行业 = ? WHERE 代码 = ?", (new_industry, test_code))
                                conn.commit()
                            else:
                                self.signals.update_log.emit(f"未能获取到通达信行业信息，可能是北交所股票或其他原因")

                                # 对于北交所股票，尝试使用其他方式获取行业信息
                                if ts_code.endswith('.BJ') or test_code.endswith('.BJ') or (test_code.isdigit() and (test_code.startswith('4') or test_code.startswith('8') or test_code.startswith('9'))):
                                    self.signals.update_log.emit("检测到北交所股票，尝试使用多种方式获取行业信息...")

                                    # 标记是否已经成功获取到行业信息
                                    industry_found = False

                                    # 方法1: 使用stock_basic接口的exchange参数
                                    if not industry_found:
                                        try:
                                            self.signals.update_log.emit("方法1: 使用stock_basic接口的exchange=BSE参数...")
                                            bj_info = self.pro.stock_basic(exchange='BSE', fields='ts_code,industry')
                                            if not bj_info.empty:
                                                bj_match = bj_info[bj_info['ts_code'] == ts_code]
                                                if not bj_match.empty and 'industry' in bj_match.columns and pd.notna(bj_match.iloc[0]['industry']):
                                                    new_industry = bj_match.iloc[0]['industry']
                                                    self.signals.update_log.emit(f"成功获取到北交所股票行业: {new_industry}")

                                                    # 更新数据库
                                                    cursor.execute("UPDATE [stocks] SET 通达信行业 = ? WHERE 代码 = ?", (new_industry, test_code))
                                                    conn.commit()
                                                    industry_found = True
                                        except Exception as e:
                                            self.signals.update_log.emit(f"方法1获取北交所股票行业信息时出错: {str(e)}")

                                    # 方法2: 使用stock_basic接口但不指定exchange参数
                                    if not industry_found:
                                        try:
                                            self.signals.update_log.emit("方法2: 使用stock_basic接口但不指定exchange参数...")
                                            all_stocks = self.pro.stock_basic(fields='ts_code,industry')
                                            if not all_stocks.empty:
                                                stock_match = all_stocks[all_stocks['ts_code'] == ts_code]
                                                if not stock_match.empty and 'industry' in stock_match.columns and pd.notna(stock_match.iloc[0]['industry']):
                                                    new_industry = stock_match.iloc[0]['industry']
                                                    self.signals.update_log.emit(f"成功获取到北交所股票行业: {new_industry}")

                                                    # 更新数据库
                                                    cursor.execute("UPDATE [stocks] SET 通达信行业 = ? WHERE 代码 = ?", (new_industry, test_code))
                                                    conn.commit()
                                                    industry_found = True
                                        except Exception as e:
                                            self.signals.update_log.emit(f"方法2获取北交所股票行业信息时出错: {str(e)}")

                                    # 方法3: 使用stock_company接口
                                    if not industry_found:
                                        try:
                                            self.signals.update_log.emit("方法3: 使用stock_company接口...")
                                            company_info = self.pro.stock_company(ts_code=ts_code, fields='ts_code,industry')
                                            if not company_info.empty and 'industry' in company_info.columns and pd.notna(company_info.iloc[0]['industry']):
                                                new_industry = company_info.iloc[0]['industry']
                                                self.signals.update_log.emit(f"成功获取到北交所股票行业: {new_industry}")

                                                # 更新数据库
                                                cursor.execute("UPDATE [stocks] SET 通达信行业 = ? WHERE 代码 = ?", (new_industry, test_code))
                                                conn.commit()
                                                industry_found = True
                                        except Exception as e:
                                            self.signals.update_log.emit(f"方法3获取北交所股票行业信息时出错: {str(e)}")

                                    # 方法4: 使用bak_basic接口
                                    if not industry_found:
                                        try:
                                            self.signals.update_log.emit("方法4: 使用bak_basic接口...")
                                            bak_info = self.pro.bak_basic(ts_code=ts_code, fields='ts_code,industry')
                                            if not bak_info.empty and 'industry' in bak_info.columns and pd.notna(bak_info.iloc[0]['industry']):
                                                new_industry = bak_info.iloc[0]['industry']
                                                self.signals.update_log.emit(f"成功获取到北交所股票行业: {new_industry}")

                                                # 更新数据库
                                                cursor.execute("UPDATE [stocks] SET 通达信行业 = ? WHERE 代码 = ?", (new_industry, test_code))
                                                conn.commit()
                                                industry_found = True
                                        except Exception as e:
                                            self.signals.update_log.emit(f"方法4获取北交所股票行业信息时出错: {str(e)}")

                                    # 如果所有方法都失败
                                    if not industry_found:
                                        self.signals.update_log.emit("所有API方法都失败，可能需要手动查询北交所股票行业信息")

                            # 显示通达信行业对比
                            industry_changed = "（已变更）" if old_industry != new_industry and old_industry != '未知' and new_industry != '未知' else ""
                            self.signals.update_log.emit(f"通达信行业: {old_industry} -> {new_industry} {industry_changed}")
                        except Exception as e:
                            self.signals.update_log.emit(f"获取通达信行业信息时出错: {str(e)}")

                        self.signals.update_log.emit("========================")

                    # 确保关闭数据库连接
                    try:
                        conn.close()
                    except:
                        pass

                    self.signals.update_log.emit(f"成功更新股票 {test_code} 的数据")
                    self.signals.finished.emit()
                    return True

                except Exception as e:
                    conn.close()
                    self.signals.error.emit(f"获取股票 {ts_code} 数据时出错: {str(e)}")
                    return False
            else:
                # 批量更新所有股票
                self.signals.update_log.emit("开始批量更新所有股票...")

                # 从config.ini读取批量大小和线程数
                config = configparser.ConfigParser()
                config.read('config.ini', encoding='utf-8')
                batch_size = int(config.get('Settings', 'batch_size', fallback='500'))

                # 确保批量大小不超过API限制
                if batch_size > 500:
                    self.signals.update_log.emit(f"警告: 配置的批量大小 {batch_size} 可能超出API限制，调整为 500")
                    batch_size = 500

                # 修改为单线程处理，避免数据库锁定问题
                thread_count = 1  # 强制使用单线程

                self.signals.update_log.emit(f"批量大小: {batch_size}, 线程数: {thread_count}")

                # 获取所有股票代码
                cursor.execute(f"SELECT 代码 FROM [{table_name}]")
                all_stocks = [row[0] for row in cursor.fetchall()]
                total_stocks = len(all_stocks)

                # 关闭数据库连接，让后续操作使用自己的连接
                conn.close()

                if total_stocks == 0:
                    self.signals.update_log.emit("数据库中没有股票数据")
                    self.signals.finished.emit()
                    return True

                self.signals.update_log.emit(f"共有 {total_stocks} 只股票需要更新")

                # 分批处理
                batches = [all_stocks[i:i+batch_size] for i in range(0, total_stocks, batch_size)]
                total_batches = len(batches)
                self.signals.update_log.emit(f"分为 {total_batches} 批处理")

                # 单线程顺序处理所有批次
                total_success = 0
                total_error = 0

                for i, batch in enumerate(batches):
                    batch_num = i + 1
                    self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 开始处理...")

                    # 处理当前批次
                    result = self._update_batch(batch, batch_num, total_batches, db_path, table_name)

                    # 获取批次处理结果
                    batch_num, success_count, error_count = result
                    total_success += success_count
                    total_error += error_count

                    # 更新进度
                    progress = int((batch_num / total_batches) * 100)
                    self.signals.update_progress.emit(progress)
                    self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 完成: 成功 {success_count}, 失败 {error_count}")

                self.signals.update_log.emit(f"所有批次处理完成: 总成功 {total_success}, 总失败 {total_error}")

                # 保存未成功更新的股票信息到日志文件
                try:
                    log_dir = "logs"
                    if not os.path.exists(log_dir):
                        os.makedirs(log_dir)

                    log_file = os.path.join(log_dir, f"update_failed_{time.strftime('%Y%m%d_%H%M%S')}.log")
                    self.signals.update_log.emit(f"正在保存未成功更新的股票信息到: {log_file}")

                    # 写入简化的总结日志
                    with open(log_file, "w", encoding="utf-8") as f:
                        f.write(f"更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"总股票数: {total_success + total_error}\n")
                        f.write(f"成功更新: {total_success}\n")
                        f.write(f"未成功更新: {total_error}\n\n")

                        # 获取所有未成功更新的股票
                        all_failed_stocks = list(db_failed_stocks.keys())

                        # 总结
                        f.write("总结:\n")
                        f.write("=" * 50 + "\n")
                        f.write(f"共有 {len(all_failed_stocks)} 只股票未成功更新，主要原因是停牌或数据缺失\n\n")

                        # 列出所有未成功更新的股票代码和名称
                        if all_failed_stocks:
                            f.write("未成功更新的股票列表:\n")
                            f.write("-" * 30 + "\n")

                            # 按代码排序
                            all_failed_stocks.sort()

                            # 每行显示一个股票
                            for code in all_failed_stocks:
                                if code in db_failed_stocks:
                                    name = db_failed_stocks[code]["name"] or "未知"
                                    f.write(f"代码: {code}, 名称: {name}\n")

                    conn.close()
                    self.signals.update_log.emit(f"未成功更新的股票信息已保存到: {log_file}")
                except Exception as e:
                    import traceback
                    self.signals.update_log.emit(f"保存未成功更新的股票信息时出错: {str(e)}\n{traceback.format_exc()}")

                self.signals.update_log.emit("所有股票更新完成")
                self.signals.update_progress.emit(100)
                self.signals.finished.emit()
                return True

        except Exception as e:
            import traceback
            error_msg = f"更新股票价格和自由流通市值时出错: {str(e)}\n{traceback.format_exc()}"
            self.signals.error.emit(error_msg)
            return False

    def _update_names_batch(self, codes, batch_num, total_batches, db_path, table_name,
                           code_to_name, code_to_area, code_to_industry, columns):
        """处理一批股票名称的更新

        Args:
            codes: 要更新的股票代码列表
            batch_num: 当前批次编号
            total_batches: 总批次数
            db_path: 数据库路径
            table_name: 表名
            code_to_name: 代码到名称的映射
            code_to_area: 代码到地区的映射
            code_to_industry: 代码到行业的映射
            columns: 数据库表的列名列表

        Returns:
            tuple: (batch_num, updated_count, not_found_count, not_found_codes)
        """
        # 使用WAL模式连接数据库，提高并发性能
        conn = sqlite3.connect(db_path, timeout=60.0)  # 增加超时时间到60秒

        # 启用WAL模式
        conn.execute("PRAGMA journal_mode=WAL")
        # 设置同步模式为NORMAL，提高性能
        conn.execute("PRAGMA synchronous=NORMAL")
        # 设置缓存大小
        conn.execute("PRAGMA cache_size=10000")

        cursor = conn.cursor()

        updated_count = 0
        not_found_count = 0
        not_found_codes = []

        try:
            # 开始事务
            conn.execute("BEGIN IMMEDIATE")

            # 记录开始时间
            start_time = time.time()
            self.signals.update_log.emit(f"批次 {batch_num} 开始处理 {len(codes)} 只股票...")

            # 准备批量更新的数据
            updates_name = []
            updates_area = [] if code_to_area else []
            updates_industry = [] if code_to_industry else []

            for code in codes:
                # 尝试多种可能的代码格式
                original_code = code.strip()
                found = False

                # 生成可能的代码格式列表
                possible_formats = [original_code]  # 原始代码

                # 如果是带前缀的代码（如SH600001）
                if original_code.upper().startswith('SH') or original_code.upper().startswith('SZ'):
                    prefix = original_code[:2].upper()
                    number_part = original_code[2:]
                    possible_formats.append(number_part)  # 纯数字部分
                    possible_formats.append(f"{number_part}.{prefix}")  # 带后缀格式 (600001.SH)

                # 如果是带后缀的代码（如600001.SH）
                elif '.' in original_code:
                    parts = original_code.split('.')
                    number_part = parts[0]
                    suffix = parts[1].upper()
                    possible_formats.append(number_part)  # 纯数字部分
                    possible_formats.append(f"{suffix}{number_part}")  # 带前缀格式 (SH600001)

                # 如果是纯数字代码（如600001）
                elif original_code.isdigit():
                    if original_code.startswith('6'):
                        possible_formats.append(f"{original_code}.SH")  # 带后缀格式
                        possible_formats.append(f"SH{original_code}")  # 带前缀格式
                    else:
                        possible_formats.append(f"{original_code}.SZ")  # 带后缀格式
                        possible_formats.append(f"SZ{original_code}")  # 带前缀格式

                # 先获取原有的名称（如果存在）
                try:
                    cursor.execute(f"SELECT 名称 FROM [{table_name}] WHERE 代码 = ?", (original_code,))
                    result = cursor.fetchone()
                    original_name = result[0] if result and result[0] else None
                except:
                    original_name = None

                # 尝试所有可能的格式
                for format_code in possible_formats:
                    if format_code in code_to_name:
                        name = code_to_name[format_code]

                        # 检查名称是否发生了重大变化
                        name_changed = False
                        if original_name:
                            # 如果原名称以"ST"开头，但新名称不是，或者反之，则认为是重大变化
                            if ('ST' in original_name) != ('ST' in name):
                                name_changed = True
                                self.signals.update_log.emit(f"警告: 股票 {original_code} 的名称发生重大变化: {original_name} -> {name}")
                            # 如果原名称包含"退市"，但新名称不是，或者反之，则认为是重大变化
                            elif ('退市' in original_name) != ('退市' in name):
                                name_changed = True
                                self.signals.update_log.emit(f"警告: 股票 {original_code} 的名称发生重大变化: {original_name} -> {name}")

                        # 如果名称发生了重大变化，保留原名称
                        if name_changed:
                            self.signals.update_log.emit(f"保留原名称: {original_name}")
                            name = original_name

                        # 收集更新数据
                        if '名称' in columns:
                            updates_name.append((name, original_code))
                            updated_count += 1

                        # 收集地区更新数据（仅当code_to_area不为None时）
                        if '省份' in columns and code_to_area and format_code in code_to_area:
                            updates_area.append((code_to_area[format_code], original_code))

                        # 收集行业更新数据（仅当code_to_industry不为None时）
                        if '申万行业' in columns and code_to_industry and format_code in code_to_industry:
                            updates_industry.append((code_to_industry[format_code], original_code))

                        found = True
                        break

                if not found:
                    not_found_count += 1
                    not_found_codes.append(original_code)

            # 执行批量更新
            if updates_name:
                self.signals.update_log.emit(f"批次 {batch_num} 更新名称 {len(updates_name)} 条记录")
                cursor.executemany(f"UPDATE [{table_name}] SET 名称 = ? WHERE 代码 = ?", updates_name)

            # 仅当code_to_area不为None时更新地区
            if code_to_area and updates_area:
                self.signals.update_log.emit(f"批次 {batch_num} 更新地区 {len(updates_area)} 条记录")
                cursor.executemany(f"UPDATE [{table_name}] SET 省份 = ? WHERE 代码 = ?", updates_area)

            # 仅当code_to_industry不为None时更新行业
            if code_to_industry and updates_industry:
                self.signals.update_log.emit(f"批次 {batch_num} 更新行业 {len(updates_industry)} 条记录")
                cursor.executemany(f"UPDATE [{table_name}] SET 申万行业 = ? WHERE 代码 = ?", updates_industry)

            # 提交事务
            conn.commit()

            # 记录结束时间
            end_time = time.time()
            elapsed = end_time - start_time
            self.signals.update_log.emit(f"批次 {batch_num} 成功提交事务，耗时 {elapsed:.2f} 秒")

        except sqlite3.Error as e:
            self.signals.update_log.emit(f"批次 {batch_num} 数据库错误: {str(e)}")
            try:
                conn.rollback()
                self.signals.update_log.emit(f"批次 {batch_num} 已回滚事务")
            except:
                pass
        except Exception as e:
            import traceback
            self.signals.update_log.emit(f"批次 {batch_num} 处理出错: {str(e)}\n{traceback.format_exc()}")
            try:
                conn.rollback()
                self.signals.update_log.emit(f"批次 {batch_num} 已回滚事务")
            except:
                pass
        finally:
            # 关闭连接前确保事务已提交或回滚
            try:
                conn.execute("PRAGMA optimize")  # 优化数据库
            except:
                pass
            conn.close()

        return batch_num, updated_count, not_found_count, not_found_codes

    def _update_batch(self, stock_codes, batch_num, total_batches, db_path, table_name):
        """处理一批股票的更新

        Args:
            stock_codes: 要更新的股票代码列表
            batch_num: 当前批次编号
            total_batches: 总批次数
            db_path: 数据库路径
            table_name: 表名

        Returns:
            tuple: (batch_num, success_count, error_count)
        """
        if not self.pro:
            return batch_num, 0, len(stock_codes)

        # 初始化股票代码信息映射
        code_to_info = {}  # 定义code_to_info变量

        # 获取所有股票的基本信息
        try:
            # 获取所有股票列表
            self.signals.update_log.emit(f"批次 {batch_num} 正在获取所有股票基本信息...")
            stock_basic = self.pro.stock_basic(exchange='', list_status='L',
                                            fields='ts_code,symbol,name,area,industry,list_date')

            # 创建代码到名称的映射
            if not stock_basic.empty:
                self.signals.update_log.emit(f"批次 {batch_num} 获取到 {len(stock_basic)} 只股票的基本信息")

                for _, row in stock_basic.iterrows():
                    ts_code = row['ts_code']
                    symbol = row['symbol']
                    name = row['name']

                    # 保存多种可能的代码格式
                    # 1. Tushare格式 (如: 000001.SZ)
                    code_to_info[ts_code] = {
                        'name': name,
                        'symbol': symbol
                    }

                    # 2. 纯数字代码 (如: 000001)
                    code_to_info[symbol] = {
                        'name': name,
                        'ts_code': ts_code
                    }

                    # 3. 带前缀的代码 (如: SZ000001)
                    if ts_code.endswith('.SH'):
                        prefix_code = f"SH{symbol}"
                        code_to_info[prefix_code] = {
                            'name': name,
                            'ts_code': ts_code
                        }
                    elif ts_code.endswith('.SZ'):
                        prefix_code = f"SZ{symbol}"
                        code_to_info[prefix_code] = {
                            'name': name,
                            'ts_code': ts_code
                        }
                    elif ts_code.endswith('.BJ'):
                        prefix_code = f"BJ{symbol}"
                        code_to_info[prefix_code] = {
                            'name': name,
                            'ts_code': ts_code
                        }
            else:
                self.signals.update_log.emit(f"批次 {batch_num} 未获取到股票基本信息")
        except Exception as e:
            self.signals.update_log.emit(f"批次 {batch_num} 获取股票基本信息失败: {str(e)}")
            # 继续执行，使用空的code_to_info字典

        # 获取最新交易日期
        try:
            # 获取当前日期和时间
            current_date = time.strftime('%Y%m%d')
            current_time = time.strftime('%H%M')

            # 获取交易日历
            self.signals.update_log.emit(f"获取交易日历...")
            trade_cal = self.pro.trade_cal(exchange='SSE', is_open=1,
                                          start_date='20230101', end_date=current_date)

            if trade_cal.empty:
                self.signals.update_log.emit(f"警告: 未获取到交易日历数据，使用当前日期")
                latest_trade_date = current_date
            else:
                # 按日期降序排序
                trade_cal = trade_cal.sort_values(by='cal_date', ascending=False)

                # 获取最新交易日
                latest_trade_date = trade_cal['cal_date'].iloc[0]
                self.signals.update_log.emit(f"最新交易日: {latest_trade_date}")

                # 检查当前日期是否是交易日
                is_today_trading_day = current_date in trade_cal['cal_date'].values

                # 如果当前日期是交易日且时间大于16:00，使用当前日期
                if is_today_trading_day and int(current_time) >= 1600:
                    latest_trade_date = current_date
                    self.signals.update_log.emit(f"当前时间大于16:00，使用当日数据: {latest_trade_date}")
                # 否则使用最近的交易日
                elif latest_trade_date != current_date:
                    self.signals.update_log.emit(f"使用最近交易日数据: {latest_trade_date}")
        except Exception as e:
            # 如果获取失败，使用当前日期
            self.signals.update_log.emit(f"获取交易日历出错: {str(e)}，使用当前日期")
            latest_trade_date = time.strftime('%Y%m%d')

        self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 开始处理 {len(stock_codes)} 只股票...")

        success_count = 0
        error_count = 0
        ts_codes = []
        original_codes = []

        try:
            for code in stock_codes:
                # 尝试多种可能的代码格式
                original_code = code.strip()
                ts_code = None

                # 如果是带后缀的代码（如600001.SH, 000001.SZ, 430047.BJ）
                if original_code.upper().endswith('.SH') or original_code.upper().endswith('.SZ') or original_code.upper().endswith('.BJ'):
                    ts_code = original_code.upper()

                # 如果是带前缀的代码（如SH600001, SZ000001, BJ430047）
                elif (original_code.upper().startswith('SH') or original_code.upper().startswith('SZ') or original_code.upper().startswith('BJ')) and original_code[2:].isdigit():
                    prefix = original_code[:2].upper()
                    number_part = original_code[2:]
                    ts_code = f"{number_part}.{prefix}"

                # 如果是纯数字代码（如600001, 000001, 430047, 920060）
                elif original_code.isdigit():
                    if original_code.startswith('6'):
                        ts_code = f"{original_code}.SH"
                    elif original_code.startswith('8') or original_code.startswith('4') or original_code.startswith('9'):
                        ts_code = f"{original_code}.BJ"  # 北交所股票通常以8、4或9开头
                    else:
                        ts_code = f"{original_code}.SZ"

                # 如果无法识别格式，尝试在映射中查找
                else:
                    if original_code in code_to_info and 'ts_code' in code_to_info[original_code]:
                        ts_code = code_to_info[original_code]['ts_code']

                if ts_code:
                    ts_codes.append(ts_code)
                    original_codes.append(original_code)
                else:
                    error_count += 1
                    continue

            # 批量获取行情数据
            if ts_codes:
                # 记录开始时间
                start_time = time.time()
                original_codes = []  # 保存原始代码，用于后续映射

                for code in stock_codes:
                    # 尝试多种可能的代码格式
                    original_code = code.strip()
                    ts_code = None

                    # 如果是带后缀的代码（如600001.SH, 000001.SZ, 430047.BJ）
                    if original_code.upper().endswith('.SH') or original_code.upper().endswith('.SZ') or original_code.upper().endswith('.BJ'):
                        ts_code = original_code.upper()

                    # 如果是带前缀的代码（如SH600001, SZ000001, BJ430047）
                    elif (original_code.upper().startswith('SH') or original_code.upper().startswith('SZ') or original_code.upper().startswith('BJ')) and original_code[2:].isdigit():
                        prefix = original_code[:2].upper()
                        number_part = original_code[2:]
                        ts_code = f"{number_part}.{prefix}"

                    # 如果是纯数字代码（如600001, 000001, 430047, 920060）
                    elif original_code.isdigit():
                        if original_code.startswith('6'):
                            ts_code = f"{original_code}.SH"
                        elif original_code.startswith('8') or original_code.startswith('4') or original_code.startswith('9'):
                            ts_code = f"{original_code}.BJ"  # 北交所股票通常以8、4或9开头
                        else:
                            ts_code = f"{original_code}.SZ"

                    # 如果无法识别格式，尝试在映射中查找
                    else:
                        if original_code in code_to_info and 'ts_code' in code_to_info[original_code]:
                            ts_code = code_to_info[original_code]['ts_code']

                    if ts_code:
                        ts_codes.append(ts_code)
                        original_codes.append(original_code)
                    else:
                        error_count += 1
                        continue

                # 批量获取行情数据
                start_time = time.time()

                # 将ts_codes列表转换为逗号分隔的字符串
                ts_codes_str = ','.join(ts_codes)

                # 获取行情数据
                self.signals.update_log.emit(f"批次 {batch_num} 正在获取行情数据，日期: {latest_trade_date}...")
                try:
                    # 检查ts_codes_str的长度，可能超出API限制
                    if len(ts_codes_str) > 5000:  # 假设API限制为5000字符
                        self.signals.update_log.emit(f"警告: ts_codes_str长度为 {len(ts_codes_str)} 字符，可能超出API限制")
                        # 记录前几个代码用于调试
                        self.signals.update_log.emit(f"前几个代码: {ts_codes_str[:100]}...")

                        # 分割为更小的批次
                        ts_codes_list = ts_codes_str.split(',')
                        self.signals.update_log.emit(f"分割为 {len(ts_codes_list)} 个代码")

                        # 如果代码数量过多，可能需要进一步分批处理
                        if len(ts_codes_list) > 500:  # 假设API限制为500个代码
                            self.signals.update_log.emit(f"警告: 代码数量为 {len(ts_codes_list)}，可能超出API限制，尝试分批获取")

                            # 分批获取数据
                            df_daily_list = []
                            batch_size = 300  # 每批300个代码
                            for j in range(0, len(ts_codes_list), batch_size):
                                sub_batch = ts_codes_list[j:j+batch_size]
                                sub_ts_codes_str = ','.join(sub_batch)
                                self.signals.update_log.emit(f"获取子批次 {j//batch_size + 1}/{(len(ts_codes_list)-1)//batch_size + 1} 行情数据，包含 {len(sub_batch)} 个代码")
                                sub_df = self.pro.daily(ts_code=sub_ts_codes_str, trade_date=latest_trade_date)
                                if not sub_df.empty:
                                    df_daily_list.append(sub_df)
                                    self.signals.update_log.emit(f"子批次获取到 {len(sub_df)} 条行情数据")
                                else:
                                    self.signals.update_log.emit(f"子批次未获取到数据")

                            # 合并所有子批次数据
                            if df_daily_list:
                                df_daily = pd.concat(df_daily_list, ignore_index=True)
                                self.signals.update_log.emit(f"合并后共获取到 {len(df_daily)} 条行情数据")
                            else:
                                df_daily = pd.DataFrame()
                                self.signals.update_log.emit(f"所有子批次均未获取到数据")
                        else:
                            # 正常获取数据
                            df_daily = self.pro.daily(ts_code=ts_codes_str, trade_date=latest_trade_date)
                    else:
                        # 正常获取数据
                        df_daily = self.pro.daily(ts_code=ts_codes_str, trade_date=latest_trade_date)

                    self.signals.update_log.emit(f"批次 {batch_num} 获取到 {len(df_daily)} 条行情数据")

                    # 检查数据结构
                    if not df_daily.empty:
                        self.signals.update_log.emit(f"行情数据列: {', '.join(df_daily.columns.tolist())}")
                        # 检查是否包含必要的列
                        if 'ts_code' not in df_daily.columns or 'close' not in df_daily.columns:
                            self.signals.update_log.emit(f"警告: 行情数据缺少必要的列！")

                    # 检查是否有数据
                    if df_daily.empty:
                        self.signals.update_log.emit(f"警告: 批次 {batch_num} 未获取到任何行情数据！尝试获取前一交易日数据...")
                        # 尝试获取前一交易日数据
                        trade_cal = self.pro.trade_cal(exchange='SSE', is_open=1,
                                                      start_date='20230101', end_date=latest_trade_date)
                        if len(trade_cal) > 1:
                            prev_trade_date = trade_cal['cal_date'].iloc[1]  # 获取前一交易日
                            self.signals.update_log.emit(f"尝试使用前一交易日: {prev_trade_date}")

                            # 确保ts_codes_list已定义
                            ts_codes_list = ts_codes_str.split(',')

                            # 同样检查代码数量
                            if len(ts_codes_list) > 500:
                                # 分批获取数据
                                df_daily_list = []
                                batch_size = 300  # 每批300个代码
                                for j in range(0, len(ts_codes_list), batch_size):
                                    sub_batch = ts_codes_list[j:j+batch_size]
                                    sub_ts_codes_str = ','.join(sub_batch)
                                    self.signals.update_log.emit(f"获取子批次 {j//batch_size + 1}/{(len(ts_codes_list)-1)//batch_size + 1} 前一交易日行情数据")
                                    sub_df = self.pro.daily(ts_code=sub_ts_codes_str, trade_date=prev_trade_date)
                                    if not sub_df.empty:
                                        df_daily_list.append(sub_df)

                                # 合并所有子批次数据
                                if df_daily_list:
                                    df_daily = pd.concat(df_daily_list, ignore_index=True)
                                    self.signals.update_log.emit(f"合并后共获取到 {len(df_daily)} 条前一交易日行情数据")
                                else:
                                    df_daily = pd.DataFrame()
                            else:
                                df_daily = self.pro.daily(ts_code=ts_codes_str, trade_date=prev_trade_date)

                            self.signals.update_log.emit(f"批次 {batch_num} 获取到 {len(df_daily)} 条行情数据(前一交易日)")
                except Exception as e:
                    self.signals.update_log.emit(f"批次 {batch_num} 获取行情数据出错: {str(e)}")
                    raise

                # 获取基本面数据
                self.signals.update_log.emit(f"批次 {batch_num} 正在获取基本面数据...")
                try:
                    # 使用与行情数据相同的分批处理逻辑
                    if len(ts_codes_str) > 5000 or (len(ts_codes_list) > 500 if 'ts_codes_list' in locals() else False):
                        # 确保ts_codes_list已定义
                        if 'ts_codes_list' not in locals():
                            ts_codes_list = ts_codes_str.split(',')

                        self.signals.update_log.emit(f"警告: 代码数量为 {len(ts_codes_list)}，可能超出API限制，尝试分批获取基本面数据")

                        # 分批获取数据
                        df_basic_list = []
                        batch_size = 300  # 每批300个代码
                        for j in range(0, len(ts_codes_list), batch_size):
                            sub_batch = ts_codes_list[j:j+batch_size]
                            sub_ts_codes_str = ','.join(sub_batch)
                            self.signals.update_log.emit(f"获取子批次 {j//batch_size + 1}/{(len(ts_codes_list)-1)//batch_size + 1} 基本面数据，包含 {len(sub_batch)} 个代码")
                            sub_df = self.pro.daily_basic(ts_code=sub_ts_codes_str, trade_date=latest_trade_date,
                                                        fields='ts_code,free_share,circ_mv')
                            if not sub_df.empty:
                                df_basic_list.append(sub_df)
                                self.signals.update_log.emit(f"子批次获取到 {len(sub_df)} 条基本面数据")
                            else:
                                self.signals.update_log.emit(f"子批次未获取到基本面数据")

                        # 合并所有子批次数据
                        if df_basic_list:
                            df_basic = pd.concat(df_basic_list, ignore_index=True)
                            self.signals.update_log.emit(f"合并后共获取到 {len(df_basic)} 条基本面数据")
                        else:
                            df_basic = pd.DataFrame()
                            self.signals.update_log.emit(f"所有子批次均未获取到基本面数据")
                    else:
                        # 正常获取数据
                        df_basic = self.pro.daily_basic(ts_code=ts_codes_str, trade_date=latest_trade_date,
                                                      fields='ts_code,free_share,circ_mv')

                    self.signals.update_log.emit(f"批次 {batch_num} 获取到 {len(df_basic)} 条基本面数据")

                    # 检查数据结构
                    if not df_basic.empty:
                        self.signals.update_log.emit(f"基本面数据列: {', '.join(df_basic.columns.tolist())}")
                        # 检查是否包含必要的列
                        if 'ts_code' not in df_basic.columns or 'free_share' not in df_basic.columns:
                            self.signals.update_log.emit(f"警告: 基本面数据缺少必要的列！")

                    # 检查是否有数据
                    if df_basic.empty:
                        self.signals.update_log.emit(f"警告: 批次 {batch_num} 未获取到任何基本面数据！尝试获取前一交易日数据...")
                        # 尝试获取前一交易日数据
                        trade_cal = self.pro.trade_cal(exchange='SSE', is_open=1,
                                                      start_date='20230101', end_date=latest_trade_date)
                        if len(trade_cal) > 1:
                            prev_trade_date = trade_cal['cal_date'].iloc[1]  # 获取前一交易日
                            self.signals.update_log.emit(f"尝试使用前一交易日: {prev_trade_date}")

                            # 确保ts_codes_list已定义
                            ts_codes_list = ts_codes_str.split(',')

                            # 同样检查代码数量
                            if len(ts_codes_list) > 500:
                                # 分批获取数据
                                df_basic_list = []
                                batch_size = 300  # 每批300个代码
                                for j in range(0, len(ts_codes_list), batch_size):
                                    sub_batch = ts_codes_list[j:j+batch_size]
                                    sub_ts_codes_str = ','.join(sub_batch)
                                    self.signals.update_log.emit(f"获取子批次 {j//batch_size + 1}/{(len(ts_codes_list)-1)//batch_size + 1} 前一交易日基本面数据")
                                    sub_df = self.pro.daily_basic(ts_code=sub_ts_codes_str, trade_date=prev_trade_date,
                                                                fields='ts_code,free_share,circ_mv')
                                    if not sub_df.empty:
                                        df_basic_list.append(sub_df)

                                # 合并所有子批次数据
                                if df_basic_list:
                                    df_basic = pd.concat(df_basic_list, ignore_index=True)
                                    self.signals.update_log.emit(f"合并后共获取到 {len(df_basic)} 条前一交易日基本面数据")
                                else:
                                    df_basic = pd.DataFrame()
                            else:
                                df_basic = self.pro.daily_basic(ts_code=ts_codes_str, trade_date=prev_trade_date,
                                                              fields='ts_code,free_share,circ_mv')

                            self.signals.update_log.emit(f"批次 {batch_num} 获取到 {len(df_basic)} 条基本面数据(前一交易日)")
                except Exception as e:
                    self.signals.update_log.emit(f"批次 {batch_num} 获取基本面数据出错: {str(e)}")
                    raise

                # 创建数据映射
                self.signals.update_log.emit(f"批次 {batch_num} 正在处理行情数据...")
                daily_data = {}
                try:
                    for _, row in df_daily.iterrows():
                        ts_code = row['ts_code']
                        if 'close' in row and pd.notna(row['close']):
                            # 保留两位小数，但作为字符串存储，以匹配数据库TEXT类型
                            price_value = round(float(row['close']), 2)
                            daily_data[ts_code] = {
                                'close': price_value
                            }
                        else:
                            self.signals.update_log.emit(f"警告: 股票 {ts_code} 缺少收盘价数据")

                    self.signals.update_log.emit(f"批次 {batch_num} 成功处理 {len(daily_data)} 条行情数据")
                except Exception as e:
                    self.signals.update_log.emit(f"批次 {batch_num} 处理行情数据出错: {str(e)}")
                    raise

                self.signals.update_log.emit(f"批次 {batch_num} 正在处理基本面数据...")
                basic_data = {}
                try:
                    for _, row in df_basic.iterrows():
                        ts_code = row['ts_code']
                        if 'free_share' in row and pd.notna(row['free_share']):
                            free_share = row['free_share'] / 10000  # 转换为亿股
                        else:
                            free_share = 1.0  # 默认值
                            self.signals.update_log.emit(f"警告: 股票 {ts_code} 缺少自由流通股数据，使用默认值 1.0 亿股")

                        basic_data[ts_code] = {
                            'free_share': round(float(free_share), 2)
                        }

                    self.signals.update_log.emit(f"批次 {batch_num} 成功处理 {len(basic_data)} 条基本面数据")
                except Exception as e:
                    self.signals.update_log.emit(f"批次 {batch_num} 处理基本面数据出错: {str(e)}")
                    raise

                # 准备批量更新数据
                self.signals.update_log.emit(f"批次 {batch_num} 正在准备更新数据...")
                updates = []  # 存储(latest_price, free_share, market_cap, name, code)元组

                # 检查数据完整性
                self.signals.update_log.emit(f"批次 {batch_num} 检查数据完整性...")
                self.signals.update_log.emit(f"批次 {batch_num} 共有 {len(ts_codes)} 个ts_code, {len(daily_data)} 个行情数据, {len(basic_data)} 个基本面数据")

                # 记录缺失的股票代码
                missing_daily_codes = []
                missing_basic_codes = []
                missing_info_codes = []

                # 记录未成功更新的原因
                failed_reasons = {}

                # 记录原始代码到ts_code的映射，用于后续日志记录
                original_to_ts_code = {original: ts for ts, original in zip(ts_codes, original_codes)}

                for i, (ts_code, original_code) in enumerate(zip(ts_codes, original_codes)):
                    # 检查是否有行情数据
                    if ts_code not in daily_data:
                        missing_daily_codes.append(ts_code)
                        failed_reasons[original_code] = f"缺少行情数据 (ts_code: {ts_code})"
                        error_count += 1
                        continue

                    # 检查是否有基本信息
                    if ts_code not in code_to_info:
                        missing_info_codes.append(ts_code)
                        if original_code in failed_reasons:
                            failed_reasons[original_code] += ", 缺少基本信息"
                        else:
                            failed_reasons[original_code] = f"缺少基本信息 (ts_code: {ts_code})"

                    # 检查是否有基本面数据
                    if ts_code not in basic_data:
                        missing_basic_codes.append(ts_code)
                        if original_code in failed_reasons:
                            failed_reasons[original_code] += ", 缺少基本面数据"
                        else:
                            failed_reasons[original_code] = f"缺少基本面数据 (ts_code: {ts_code})"

                    # 获取股价 - 确保作为字符串处理
                    latest_price = str(daily_data[ts_code]['close'])

                    # 获取名称 - 尝试多种可能的代码格式
                    name = "未知"
                    # 1. 尝试直接使用ts_code
                    if ts_code in code_to_info:
                        name = code_to_info[ts_code]['name']
                    else:
                        # 2. 尝试使用纯数字代码
                        symbol = ts_code.split('.')[0] if '.' in ts_code else ts_code
                        if symbol in code_to_info:
                            name = code_to_info[symbol]['name']
                        else:
                            # 3. 尝试使用带前缀的代码
                            if '.' in ts_code:
                                parts = ts_code.split('.')
                                prefix_code = f"{parts[1]}{parts[0]}"  # 如 SZ000001
                                if prefix_code in code_to_info:
                                    name = code_to_info[prefix_code]['name']

                    # 如果仍然未找到，记录日志
                    if name == "未知":
                        self.signals.update_log.emit(f"警告: 未找到股票 {ts_code} 的名称信息")

                    # 获取自由流通股
                    if ts_code in basic_data:
                        free_share = basic_data[ts_code]['free_share']
                    else:
                        free_share = 1.0
                        missing_basic_codes.append(ts_code)
                        self.signals.update_log.emit(f"警告: 股票 {ts_code} 缺少基本面数据，使用默认值 1.0 亿股")

                    # 计算市值
                    try:
                        # 使用浮点数进行计算，但确保股价作为字符串存储
                        price_float = float(latest_price)
                        market_cap = round(price_float * float(free_share), 2)
                    except (ValueError, TypeError) as e:
                        self.signals.update_log.emit(f"警告: 计算市值出错 {ts_code}, 价格={latest_price}, 流通股={free_share}, 错误: {str(e)}")
                        market_cap = 0.0

                    # 添加到更新列表 - 确保股价作为字符串
                    updates.append((latest_price, free_share, market_cap, name, original_code))
                    success_count += 1

                # 记录缺失数据情况
                if missing_daily_codes:
                    self.signals.update_log.emit(f"批次 {batch_num} 有 {len(missing_daily_codes)} 个股票缺少行情数据")
                    if len(missing_daily_codes) <= 5:
                        self.signals.update_log.emit(f"缺少行情数据的股票: {', '.join(missing_daily_codes)}")
                    else:
                        self.signals.update_log.emit(f"部分缺少行情数据的股票: {', '.join(missing_daily_codes[:5])}...")

                if missing_basic_codes:
                    self.signals.update_log.emit(f"批次 {batch_num} 有 {len(missing_basic_codes)} 个股票缺少基本面数据")
                    if len(missing_basic_codes) <= 5:
                        self.signals.update_log.emit(f"缺少基本面数据的股票: {', '.join(missing_basic_codes)}")
                    else:
                        self.signals.update_log.emit(f"部分缺少基本面数据的股票: {', '.join(missing_basic_codes[:5])}...")

                if missing_info_codes:
                    self.signals.update_log.emit(f"批次 {batch_num} 有 {len(missing_info_codes)} 个股票缺少基本信息")
                    if len(missing_info_codes) <= 5:
                        self.signals.update_log.emit(f"缺少基本信息的股票: {', '.join(missing_info_codes)}")
                    else:
                        self.signals.update_log.emit(f"部分缺少基本信息的股票: {', '.join(missing_info_codes[:5])}...")

                # 记录失败原因，但不单独保存每个批次的日志
                # 只在内存中保存失败原因，最后在总日志中汇总
                if failed_reasons:
                    self.signals.update_log.emit(f"批次 {batch_num} 有 {len(failed_reasons)} 只股票未成功更新")

                # 使用WAL模式连接数据库，提高并发性能
                max_retries = 5
                retry_count = 0
                timeout = 60  # 60秒超时

                while retry_count < max_retries:
                    try:
                        # 使用超时参数连接数据库
                        conn = sqlite3.connect(db_path, timeout=timeout)

                        # 启用WAL模式，提高并发性能
                        conn.execute("PRAGMA journal_mode=WAL")
                        # 设置更宽松的锁定模式
                        conn.execute("PRAGMA synchronous=NORMAL")
                        # 设置缓存大小
                        conn.execute("PRAGMA cache_size=10000")

                        cursor = conn.cursor()

                        # 开始事务
                        conn.execute("BEGIN IMMEDIATE TRANSACTION")

                        # 批量更新数据库
                        if updates:
                            self.signals.update_log.emit(f"批次 {batch_num} 正在执行批量更新，共 {len(updates)} 条记录...")
                            cursor.executemany(f"""
                                UPDATE [{table_name}]
                                SET 股价 = ?, 流通股 = ?, 市值Z = ?, 名称 = ?
                                WHERE 代码 = ?
                            """, updates)

                            # 提交事务
                            conn.commit()

                            # 记录结束时间
                            end_time = time.time()
                            elapsed = end_time - start_time
                            self.signals.update_log.emit(f"批次 {batch_num} 成功提交事务，耗时 {elapsed:.2f} 秒")

                            # 成功完成，跳出重试循环
                            break
                        else:
                            conn.rollback()
                            self.signals.update_log.emit(f"批次 {batch_num} 没有需要更新的数据")
                            break

                    except sqlite3.OperationalError as e:
                        # 如果是数据库锁定错误，重试
                        if "database is locked" in str(e):
                            retry_count += 1
                            self.signals.update_log.emit(f"批次 {batch_num} 数据库错误: {str(e)}，正在重试 ({retry_count}/{max_retries})...")
                            # 回滚事务
                            try:
                                conn.rollback()
                            except:
                                pass
                            # 关闭连接
                            try:
                                conn.close()
                            except:
                                pass

                # 如果达到最大重试次数仍未成功
                if retry_count >= max_retries:
                    self.signals.update_log.emit(f"批次 {batch_num} 处理失败: 达到最大重试次数")
                    error_count = len(stock_codes) - success_count

        except Exception as e:
            import traceback
            self.signals.update_log.emit(f"批次 {batch_num} 处理出错: {str(e)}\n{traceback.format_exc()}")
            error_count = len(stock_codes) - success_count

        self.signals.update_log.emit(f"批次 {batch_num}/{total_batches} 完成: 成功 {success_count}, 失败 {error_count}")
        return batch_num, success_count, error_count


    # 同花顺概念板块相关方法已移除

    def _update_stock_concepts(self, db_path, table_name):
        """更新股票的概念字段（已废弃，仅保留方法签名以避免调用错误）"""
        self.signals.update_log.emit("同花顺概念板块功能已移除")
        return

    def remove_duplicate_stocks(self, db_path='stock.db', table_name='stocks'):
        """去除数据库中的重复股票数据，以代码为主键

        Args:
            db_path: 数据库文件路径
            table_name: 表名
        """
        if not os.path.exists(db_path):
            self.signals.error.emit(f"数据库文件不存在: {db_path}")
            return False

        try:
            self.signals.update_log.emit("开始检查并去除重复的股票数据...")
            self.signals.update_progress.emit(10)

            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                conn.close()
                self.signals.error.emit(f"数据表不存在: {table_name}")
                return False

            # 获取所有股票代码
            self.signals.update_log.emit("正在获取所有股票代码...")
            self.signals.update_progress.emit(15)

            cursor.execute(f"SELECT 代码 FROM [{table_name}]")
            all_codes = [row[0] for row in cursor.fetchall()]

            # 创建标准化代码映射
            self.signals.update_log.emit("正在分析股票代码格式...")
            self.signals.update_progress.emit(20)

            # 用于存储标准化代码到原始代码的映射
            normalized_to_original = {}
            # 用于存储原始代码到标准化代码的映射
            original_to_normalized = {}

            for code in all_codes:
                # 标准化代码（去除前缀和后缀，只保留数字部分）
                normalized_code = self._normalize_stock_code(code)

                if normalized_code:
                    # 如果这个标准化代码已经存在，说明有重复
                    if normalized_code in normalized_to_original:
                        normalized_to_original[normalized_code].append(code)
                    else:
                        normalized_to_original[normalized_code] = [code]

                    # 记录原始代码到标准化代码的映射
                    original_to_normalized[code] = normalized_code

            # 找出有重复的标准化代码
            duplicates = {norm_code: codes for norm_code, codes in normalized_to_original.items() if len(codes) > 1}

            if not duplicates:
                self.signals.update_log.emit("未发现重复的股票代码，数据库已是最新状态")
                self.signals.update_progress.emit(100)
                conn.close()
                self.signals.finished.emit()
                return True

            self.signals.update_log.emit(f"发现 {len(duplicates)} 个重复的股票代码组")
            for norm_code, codes in duplicates.items():
                self.signals.update_log.emit(f"标准化代码 {norm_code} 对应的重复代码: {', '.join(codes)}")

            self.signals.update_progress.emit(30)

            # 创建临时表存储要保留的记录
            self.signals.update_log.emit("正在创建临时表...")
            cursor.execute(f"CREATE TEMPORARY TABLE temp_{table_name} AS SELECT * FROM [{table_name}] WHERE 0")

            # 处理每个重复的股票代码组
            total_duplicates = len(duplicates)
            processed = 0

            # 记录要保留的代码和要删除的代码
            codes_to_keep = []
            codes_to_delete = []

            for norm_code, dup_codes in duplicates.items():
                self.signals.update_log.emit(f"处理标准化代码 {norm_code}，共有 {len(dup_codes)} 条记录")

                # 选择保留哪个代码
                # 优先选择带后缀的标准格式代码（如000001.SZ），而不是纯数字代码
                suffix_codes = [c for c in dup_codes if '.' in c]  # 带后缀的代码，如000001.SZ
                if suffix_codes:
                    # 如果有带后缀的代码，选择第一个
                    code_to_keep = suffix_codes[0]
                else:
                    # 否则选择第一个代码
                    code_to_keep = dup_codes[0]

                codes_to_keep.append(code_to_keep)
                codes_to_delete.extend([c for c in dup_codes if c != code_to_keep])

                self.signals.update_log.emit(f"  - 保留代码: {code_to_keep}")
                self.signals.update_log.emit(f"  - 删除代码: {', '.join([c for c in dup_codes if c != code_to_keep])}")

                processed += 1
                progress = 30 + int((processed / total_duplicates) * 30)
                self.signals.update_progress.emit(progress)

            # 将要保留的记录插入临时表
            self.signals.update_log.emit("正在保存要保留的记录...")
            for code in codes_to_keep:
                cursor.execute(f"SELECT * FROM [{table_name}] WHERE 代码 = ?", (code,))
                records = cursor.fetchall()

                if records:
                    # 选择最新的记录（通常是最后一条）
                    latest_record = records[-1]

                    # 将记录插入临时表
                    placeholders = ", ".join(["?"] * len(latest_record))
                    cursor.execute(f"INSERT INTO temp_{table_name} VALUES ({placeholders})", latest_record)

            # 获取非重复记录（不在任何重复组中的记录）
            self.signals.update_log.emit("正在获取非重复记录...")
            non_duplicate_codes = [code for code in all_codes if code not in codes_to_keep and code not in codes_to_delete]

            for code in non_duplicate_codes:
                cursor.execute(f"SELECT * FROM [{table_name}] WHERE 代码 = ?", (code,))
                records = cursor.fetchall()

                if records:
                    # 选择最新的记录
                    latest_record = records[-1]

                    # 将记录插入临时表
                    placeholders = ", ".join(["?"] * len(latest_record))
                    cursor.execute(f"INSERT INTO temp_{table_name} VALUES ({placeholders})", latest_record)

            # 获取临时表中的记录数
            cursor.execute(f"SELECT COUNT(*) FROM temp_{table_name}")
            temp_count = cursor.fetchone()[0]

            # 获取原表中的记录数
            cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
            original_count = cursor.fetchone()[0]

            self.signals.update_log.emit(f"原表记录数: {original_count}, 去重后记录数: {temp_count}")
            self.signals.update_progress.emit(70)

            # 备份原表
            backup_table = f"{table_name}_backup_{int(time.time())}"
            self.signals.update_log.emit(f"正在备份原表到 {backup_table}...")
            cursor.execute(f"CREATE TABLE [{backup_table}] AS SELECT * FROM [{table_name}]")

            # 删除原表内容
            self.signals.update_log.emit("正在清空原表...")
            cursor.execute(f"DELETE FROM [{table_name}]")

            # 从临时表恢复数据到原表
            self.signals.update_log.emit("正在恢复去重后的数据...")
            cursor.execute(f"INSERT INTO [{table_name}] SELECT * FROM temp_{table_name}")

            # 提交事务
            conn.commit()

            # 验证记录数
            cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
            new_count = cursor.fetchone()[0]

            self.signals.update_log.emit(f"去重完成！原表记录数: {original_count}, 去重后记录数: {new_count}, 减少了 {original_count - new_count} 条记录")
            self.signals.update_log.emit(f"原表已备份到 {backup_table}")

            # 关闭连接
            conn.close()

            self.signals.update_progress.emit(100)
            self.signals.finished.emit()
            return True

        except Exception as e:
            import traceback
            error_msg = f"去除重复股票数据时出错: {str(e)}\n{traceback.format_exc()}"
            self.signals.error.emit(error_msg)
            return False

    def _normalize_stock_code(self, code):
        """标准化股票代码，去除前缀和后缀，只保留数字部分

        Args:
            code: 原始股票代码，如：000001, 000001.SZ, SZ000001

        Returns:
            标准化后的代码，如：000001
        """
        if not code:
            return None

        # 去除空格
        code = code.strip()

        # 如果是带后缀的代码（如000001.SZ）
        if '.' in code:
            code = code.split('.')[0]

        # 如果是带前缀的代码（如SZ000001）
        elif (code.upper().startswith('SH') or code.upper().startswith('SZ') or code.upper().startswith('BJ')) and len(code) > 2 and code[2:].isdigit():
            code = code[2:]

        # 确保是有效的股票代码（6位数字）
        if code.isdigit() and len(code) == 6:
            return code

        return None

class StockUpdaterWidget(QWidget):
    """股票更新界面，用于集成到主GUI"""

    def __init__(self):
        super().__init__()
        self.initUI()
        self.updater = None
        self.update_thread = None

    def initUI(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建标题
        title_label = QLabel('股票数据更新')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建文件信息区域
        info_group = QGroupBox('数据库信息')
        info_layout = QVBoxLayout()

        self.db_path_label = QLabel('数据库文件: stock.db')
        self.table_name_label = QLabel('数据表: stocks')

        info_layout.addWidget(self.db_path_label)
        info_layout.addWidget(self.table_name_label)
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)

        # 创建测试区域
        test_group = QGroupBox('测试功能')
        test_layout = QVBoxLayout()

        # 测试单只股票的区域
        stock_test_layout = QHBoxLayout()
        test_label = QLabel('股票代码:')
        self.test_code_edit = QLineEdit()
        self.test_code_edit.setPlaceholderText('输入股票代码，如: 000001')
        self.test_button = QPushButton('测试更新(含通达信行业)')
        self.test_button.clicked.connect(self.test_stock_update)
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
        """)

        stock_test_layout.addWidget(test_label)
        stock_test_layout.addWidget(self.test_code_edit)
        stock_test_layout.addWidget(self.test_button)
        test_layout.addLayout(stock_test_layout)

        # 同花顺概念板块相关按钮已移除

        test_group.setLayout(test_layout)

        test_group.setLayout(test_layout)
        main_layout.addWidget(test_group)

        # 创建操作按钮
        buttons_layout = QHBoxLayout()

        self.update_all_data_button = QPushButton('更新名称、价格、市值、流通股、通达信行业')
        self.update_all_data_button.clicked.connect(self.update_all_stock_data)
        self.update_all_data_button.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                padding: 10px;
                font-size: 12pt;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
        """)

        self.remove_duplicates_button = QPushButton('去除重复数据(以代码为主键)')
        self.remove_duplicates_button.clicked.connect(self.remove_duplicate_data)
        self.remove_duplicates_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 10px;
                font-size: 12pt;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #EF6C00;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.update_all_data_button)
        buttons_layout.addWidget(self.remove_duplicates_button)
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

        # 创建进度条
        progress_group = QGroupBox('更新进度')
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        self.status_label = QLabel('准备就绪')

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # 创建日志区域
        log_group = QGroupBox('操作日志')
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置布局
        self.setLayout(main_layout)

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_log(self, message):
        """更新日志显示"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, '错误', message)
        self.update_finished()

    def update_finished(self):
        """更新完成后的处理"""
        self.update_all_data_button.setEnabled(True)
        self.test_button.setEnabled(True)
        self.status_label.setText('更新完成')

    def update_stock_names(self):
        """更新股票名称"""
        # 禁用按钮
        self.update_names_button.setEnabled(False)
        self.update_prices_button.setEnabled(False)
        self.update_all_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText('正在更新股票名称...')

        # 添加日志
        self.log_text.append("开始更新股票名称（不更新行业和地区信息）...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 创建并启动更新线程
        self.update_thread = threading.Thread(
            target=self.updater.update_stock_names,
            args=('stock.db', 'stocks', False, False)  # 不更新行业和地区
        )
        self.update_thread.daemon = True
        self.update_thread.start()

    def update_stock_prices(self):
        """更新所有股票价格和自由流通市值"""
        # 禁用按钮
        self.update_names_button.setEnabled(False)
        self.update_prices_button.setEnabled(False)
        self.update_all_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText('正在更新股票价格和自由流通市值...')

        # 添加日志
        self.log_text.append("开始更新股票价格和自由流通市值...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 创建并启动更新线程
        self.update_thread = threading.Thread(
            target=self.updater.update_stock_prices_and_market_cap,
            args=('stock.db', 'stocks', None)
        )
        self.update_thread.daemon = True
        self.update_thread.start()

    def update_stock_all(self):
        """更新所有股票名称、价格和自由流通市值"""
        # 禁用按钮
        self.update_names_button.setEnabled(False)
        self.update_prices_button.setEnabled(False)
        self.update_all_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText('正在更新股票名称、价格和自由流通市值...')

        # 添加日志
        self.log_text.append("开始更新股票名称、价格和自由流通市值...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 先更新名称，然后更新价格和市值
        def update_all():
            # 先更新名称（不更新行业和地区）
            self.updater.update_stock_names('stock.db', 'stocks', update_industry=False, update_area=False)
            # 然后更新价格和市值
            self.updater.update_stock_prices_and_market_cap('stock.db', 'stocks', None)

        # 创建并启动更新线程
        self.update_thread = threading.Thread(target=update_all)
        self.update_thread.daemon = True
        self.update_thread.start()

    def update_all_stock_data(self):
        """更新所有股票的名称、价格、市值和自由流通股"""
        # 禁用按钮
        self.update_all_data_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText('正在更新股票名称、价格、市值、流通股、通达信行业...')

        # 添加日志
        self.log_text.append("开始更新股票名称、价格、市值、流通股、通达信行业...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 先更新名称，然后更新价格、市值、自由流通股和通达信行业
        def update_all():
            # 先检查数据库中是否有"通达信行业"列，如果没有则添加
            try:
                conn = sqlite3.connect('stock.db')
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(stocks)")
                columns = [column[1] for column in cursor.fetchall()]

                if '通达信行业' not in columns:
                    self.log_text.append("正在添加'通达信行业'列...")
                    cursor.execute("ALTER TABLE stocks ADD COLUMN 通达信行业 TEXT")
                    conn.commit()
                    self.log_text.append("成功添加'通达信行业'列")

                conn.close()
            except Exception as e:
                self.log_text.append(f"检查或添加'通达信行业'列时出错: {str(e)}")

            # 先更新名称（不更新行业和地区）
            self.updater.update_stock_names('stock.db', 'stocks', update_industry=False, update_area=False)

            # 然后更新价格、市值和自由流通股
            self.updater.update_stock_prices_and_market_cap('stock.db', 'stocks', None)

            # 最后更新通达信行业
            self.log_text.append("开始更新通达信行业数据...")
            try:
                # 获取所有股票的行业信息
                self.log_text.append("正在获取上海和深圳股票的行业信息...")
                sh_sz_industry = self.updater.pro.stock_basic(exchange='', list_status='L', fields='ts_code,industry,market')
                self.log_text.append(f"获取到 {len(sh_sz_industry)} 只上海和深圳股票")

                # 检查是否已经包含北交所股票
                bj_count_in_shsz = len(sh_sz_industry[sh_sz_industry['ts_code'].str.endswith('.BJ')])
                self.log_text.append(f"其中包含 {bj_count_in_shsz} 只北交所股票")

                # 如果上海深圳数据中不包含北交所股票，则单独获取
                if bj_count_in_shsz == 0:
                    self.log_text.append("正在获取北交所股票的行业信息...")
                    bj_industry = self.updater.pro.stock_basic(exchange='BSE', list_status='L', fields='ts_code,industry,market')
                    self.log_text.append(f"获取到 {len(bj_industry)} 只北交所股票")

                    # 合并上海、深圳和北交所的行业数据
                    stock_industry = pd.concat([sh_sz_industry, bj_industry], ignore_index=True)
                    self.log_text.append(f"合并后共有 {len(stock_industry)} 只股票")
                else:
                    # 如果已经包含北交所股票，直接使用
                    stock_industry = sh_sz_industry
                    self.log_text.append("上海深圳数据已包含北交所股票，无需单独获取")

                # 去除可能的重复项
                stock_industry = stock_industry.drop_duplicates(subset=['ts_code'])
                self.log_text.append(f"去重后共有 {len(stock_industry)} 只股票")

                if not stock_industry.empty:
                    self.log_text.append(f"成功获取 {len(stock_industry)} 只股票的行业信息")

                    # 连接数据库
                    conn = sqlite3.connect('stock.db')
                    cursor = conn.cursor()

                    # 获取所有股票代码
                    cursor.execute("SELECT 代码 FROM stocks")
                    db_codes = [row[0] for row in cursor.fetchall()]
                    self.log_text.append(f"数据库中共有 {len(db_codes)} 只股票")

                    # 更新计数
                    updated_count = 0
                    not_found_count = 0
                    not_found_codes = []

                    # 创建进度条
                    total = len(db_codes)
                    progress_step = max(1, total // 10)  # 每10%更新一次进度

                    for i, code in enumerate(db_codes):
                        # 显示进度
                        if i % progress_step == 0:
                            progress = int((i / total) * 100)
                            self.log_text.append(f"处理进度: {progress}% ({i}/{total})")

                        # 尝试转换为Tushare格式的代码
                        ts_code = None
                        original_code = code.strip()

                        # 如果是带后缀的代码（如600001.SH, 000001.SZ, 430047.BJ）
                        if original_code.upper().endswith('.SH') or original_code.upper().endswith('.SZ') or original_code.upper().endswith('.BJ'):
                            ts_code = original_code.upper()
                        # 如果是带前缀的代码（如SH600001, SZ000001, BJ430047）
                        elif (original_code.upper().startswith('SH') or original_code.upper().startswith('SZ') or original_code.upper().startswith('BJ')) and original_code[2:].isdigit():
                            prefix = original_code[:2].upper()
                            number_part = original_code[2:]
                            ts_code = f"{number_part}.{prefix}"
                        # 如果是纯数字代码（如600001, 000001, 430047, 920060）
                        elif original_code.isdigit():
                            if original_code.startswith('688'):
                                # 科创板股票
                                ts_code = f"{original_code}.SH"
                            elif original_code.startswith('6'):
                                # 上海主板股票
                                ts_code = f"{original_code}.SH"
                            elif original_code.startswith('3'):
                                # 创业板股票
                                ts_code = f"{original_code}.SZ"
                            elif original_code.startswith('00'):
                                # 深圳主板股票
                                ts_code = f"{original_code}.SZ"
                            elif original_code.startswith('4') or original_code.startswith('8') or original_code.startswith('9'):
                                # 北交所股票
                                ts_code = f"{original_code}.BJ"
                            else:
                                # 其他股票，默认尝试深圳
                                ts_code = f"{original_code}.SZ"

                        if ts_code:
                            # 在行业数据中查找
                            industry_data = stock_industry[stock_industry['ts_code'] == ts_code]
                            if not industry_data.empty and 'industry' in industry_data.columns and pd.notna(industry_data.iloc[0]['industry']):
                                industry = industry_data.iloc[0]['industry']
                                # 更新数据库
                                cursor.execute("UPDATE stocks SET 通达信行业 = ? WHERE 代码 = ?", (industry, original_code))
                                updated_count += 1
                            else:
                                # 记录未找到行业的股票
                                not_found_count += 1
                                if len(not_found_codes) < 10:  # 只记录前10个，避免日志过长
                                    not_found_codes.append(f"{original_code} (转换为 {ts_code})")

                    # 提交事务
                    conn.commit()

                    # 显示未找到行业的股票
                    if not_found_count > 0:
                        self.log_text.append(f"有 {not_found_count} 只股票未找到通达信行业数据")
                        if not_found_codes:
                            self.log_text.append(f"部分未找到行业的股票: {', '.join(not_found_codes)}")
                            if not_found_count > 10:
                                self.log_text.append(f"... 以及其他 {not_found_count - 10} 只股票")

                    # 检查北交所股票的更新情况（4、8、9开头或.BJ后缀）
                    cursor.execute("SELECT COUNT(*) FROM stocks WHERE 代码 LIKE '%.BJ' OR 代码 LIKE 'BJ%' OR (代码 GLOB '[0-9]*' AND (代码 GLOB '4*' OR 代码 GLOB '8*' OR 代码 GLOB '9*'))")
                    bj_total = cursor.fetchone()[0]

                    cursor.execute("SELECT COUNT(*) FROM stocks WHERE (代码 LIKE '%.BJ' OR 代码 LIKE 'BJ%' OR (代码 GLOB '[0-9]*' AND (代码 GLOB '4*' OR 代码 GLOB '8*' OR 代码 GLOB '9*'))) AND 通达信行业 IS NOT NULL AND 通达信行业 != ''")
                    bj_updated = cursor.fetchone()[0]

                    self.log_text.append(f"北交所股票: 共 {bj_total} 只，成功更新 {bj_updated} 只，未更新 {bj_total - bj_updated} 只")

                    conn.close()

                    self.log_text.append(f"成功更新 {updated_count} 只股票的通达信行业数据，未更新 {len(db_codes) - updated_count} 只")
                else:
                    self.log_text.append("未能获取股票行业信息")
            except Exception as e:
                self.log_text.append(f"更新通达信行业数据时出错: {str(e)}")

        # 创建并启动更新线程
        self.update_thread = threading.Thread(target=update_all)
        self.update_thread.daemon = True
        self.update_thread.start()

    def test_stock_update(self):
        """测试更新单只股票"""
        # 获取股票代码
        test_code = self.test_code_edit.text().strip()
        if not test_code:
            QMessageBox.warning(self, '警告', '请输入股票代码')
            return

        # 禁用按钮
        self.update_all_data_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText(f'正在测试更新股票 {test_code}...')

        # 添加日志
        self.log_text.append(f"开始测试更新股票 {test_code}...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 先检查数据库中是否有"通达信行业"列，如果没有则添加
        def test_update():
            try:
                conn = sqlite3.connect('stock.db')
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(stocks)")
                columns = [column[1] for column in cursor.fetchall()]

                if '通达信行业' not in columns:
                    self.log_text.append("正在添加'通达信行业'列...")
                    cursor.execute("ALTER TABLE stocks ADD COLUMN 通达信行业 TEXT")
                    conn.commit()
                    self.log_text.append("成功添加'通达信行业'列")

                conn.close()
            except Exception as e:
                self.log_text.append(f"检查或添加'通达信行业'列时出错: {str(e)}")

            # 更新价格、市值、自由流通股和通达信行业
            # update_stock_prices_and_market_cap方法已经包含了通达信行业的更新
            self.log_text.append(f"开始更新股票 {test_code} 的价格、市值、流通股和通达信行业数据...")
            self.updater.update_stock_prices_and_market_cap('stock.db', 'stocks', test_code)

        # 创建并启动更新线程
        self.update_thread = threading.Thread(target=test_update)
        self.update_thread.daemon = True
        self.update_thread.start()

    # 同花顺概念板块相关方法已移除

    def remove_duplicate_data(self):
        """去除数据库中的重复股票数据"""
        # 显示确认对话框
        reply = QMessageBox.question(
            self,
            '确认操作',
            '此操作将去除数据库中的重复股票数据，以代码为主键。\n'
            '操作前会自动备份原表，但仍建议手动备份数据库。\n\n'
            '确定要继续吗？',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用按钮
        self.update_all_data_button.setEnabled(False)
        self.remove_duplicates_button.setEnabled(False)
        self.test_button.setEnabled(False)

        # 重置进度条
        self.progress_bar.setValue(0)
        self.status_label.setText('正在去除重复数据...')

        # 添加日志
        self.log_text.clear()
        self.log_text.append("开始去除数据库中的重复股票数据...")

        # 创建更新器
        self.updater = StockUpdater()

        # 连接信号
        self.updater.signals.update_log.connect(self.update_log)
        self.updater.signals.update_progress.connect(self.update_progress)
        self.updater.signals.finished.connect(self.update_finished)
        self.updater.signals.error.connect(self.show_error)

        # 创建并启动处理线程
        self.update_thread = threading.Thread(
            target=self.updater.remove_duplicate_stocks,
            args=('stock.db', 'stocks')
        )
        self.update_thread.daemon = True
        self.update_thread.start()


# 测试代码
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    window = StockUpdaterWidget()
    window.show()
    sys.exit(app.exec_())
