"""
CURSOR硬件识别重置工具(GUI版) - 仅修改CURSOR用于识别的关键参数
注意：此脚本尽量减少对其他软件的影响
"""
import os
import sys
import random
import subprocess
import winreg
import uuid
import ctypes
import shutil
import tempfile
import json
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

class CursorResetApp:
    def __init__(self, root):
        self.root = root
        self.root.title("CURSOR硬件识别重置工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 不使用图标，避免因图标问题导致程序崩溃
        # self.root.iconbitmap(default="SYSTEMICON")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("微软雅黑", 10))
        self.style.configure("TCheckbutton", font=("微软雅黑", 10))
        self.style.configure("TLabel", font=("微软雅黑", 10))
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="CURSOR硬件识别重置工具", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明文本
        desc_label = ttk.Label(main_frame, text="此工具将重置CURSOR用于识别设备的参数，尽量不影响其他软件。", wraplength=550)
        desc_label.pack(pady=5)
        
        # 按钮区域 - 移到上方
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.reset_button = ttk.Button(button_frame, text="开始重置", command=self.start_reset)
        self.reset_button.pack(side=tk.LEFT, padx=5)
        
        self.exit_button = ttk.Button(button_frame, text="退出", command=root.destroy)
        self.exit_button.pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(button_frame, orient=tk.HORIZONTAL, length=400, mode='determinate')
        self.progress.pack(side=tk.TOP, fill=tk.X, pady=5)
        
        # 选项框架
        options_frame = ttk.LabelFrame(main_frame, text="重置选项", padding="10")
        options_frame.pack(fill=tk.X, pady=10)
        
        # 复选框
        self.reset_app_data_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="重置CURSOR应用数据", variable=self.reset_app_data_var).grid(row=0, column=0, sticky="w", pady=2)
        
        self.reset_browser_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="重置浏览器中的CURSOR存储", variable=self.reset_browser_var).grid(row=1, column=0, sticky="w", pady=2)
        
        self.clear_cookies_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="清除CURSOR相关Cookie", variable=self.clear_cookies_var).grid(row=2, column=0, sticky="w", pady=2)
        
        self.modify_registry_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="修改注册表中CURSOR相关项", variable=self.modify_registry_var).grid(row=3, column=0, sticky="w", pady=2)
        
        # 添加新选项
        self.reset_network_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="重置网络标识（MAC地址等）", variable=self.reset_network_var).grid(row=4, column=0, sticky="w", pady=2)
        
        self.clear_all_browser_data_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="清除所有浏览器数据（更彻底但会影响其他网站）", variable=self.clear_all_browser_data_var).grid(row=5, column=0, sticky="w", pady=2)
        
        # 高级选项
        advanced_frame = ttk.LabelFrame(main_frame, text="高级选项", padding="10")
        advanced_frame.pack(fill=tk.X, pady=10)
        
        self.backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="备份修改的文件", variable=self.backup_var).grid(row=0, column=0, sticky="w", pady=2)
        
        self.use_random_hwid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="使用随机硬件ID", variable=self.use_random_hwid_var).grid(row=1, column=0, sticky="w", pady=2)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=70, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 检查管理员权限
        if not self.is_admin():
            self.log("警告: 此程序需要管理员权限才能完全发挥作用。", "warning")
            messagebox.showwarning("需要管理员权限", 
                                "此程序需要管理员权限才能完全发挥作用。\n请右键点击程序，选择'以管理员身份运行'。")
    
    def is_admin(self):
        """检查是否具有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def log(self, message, level="info"):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        
        # 根据日志级别设置颜色
        tag = None
        if level == "error":
            tag = "error"
            self.log_text.tag_config("error", foreground="red")
        elif level == "success":
            tag = "success"
            self.log_text.tag_config("success", foreground="green")
        elif level == "warning":
            tag = "warning"
            self.log_text.tag_config("warning", foreground="orange")
        
        # 添加时间戳
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry, tag)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 更新状态栏
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def start_reset(self):
        """开始重置过程"""
        # 禁用按钮，防止重复点击
        self.reset_button.config(state=tk.DISABLED)
        
        # 确认对话框
        if not messagebox.askyesno("确认操作", "确定要重置CURSOR硬件识别参数吗？\n此操作将修改CURSOR相关的配置文件和注册表项。"):
            self.reset_button.config(state=tk.NORMAL)
            return
        
        # 在新线程中执行重置操作
        threading.Thread(target=self.perform_reset, daemon=True).start()
    
    def perform_reset(self):
        """执行重置操作"""
        try:
            self.log("开始重置CURSOR硬件识别...", "info")
            self.progress["value"] = 0
            
            # 计算总步骤数
            total_steps = 4  # 简化为固定步骤数
            step_value = 100 / total_steps
            current_step = 0
            
            # 首先终止CURSOR进程
            self.terminate_cursor_process()
            current_step += 1
            self.progress["value"] = current_step * step_value
            self.root.update_idletasks()
            
            # 重置CURSOR应用数据
            self.reset_cursor_app_data()
            current_step += 1
            self.progress["value"] = current_step * step_value
            self.root.update_idletasks()
            
            # 清除CURSOR相关的Cookie
            self.clear_cursor_cookies()
            current_step += 1
            self.progress["value"] = current_step * step_value
            self.root.update_idletasks()
            
            # 重置网络标识和修改注册表 (合并为一个步骤，更接近成功案例)
            self.reset_network_identity()
            current_step += 1
            self.progress["value"] = current_step * step_value
            self.root.update_idletasks()
            
            self.progress["value"] = 100
            messagebox.showinfo("操作完成", "CURSOR硬件识别重置完成！\n请重新启动CURSOR应用。\n如果仍然出现问题，请尝试重启电脑后再使用CURSOR。")
        
        except Exception as e:
            self.log(f"操作过程中出错: {str(e)}", "error")
            messagebox.showerror("错误", f"操作过程中出错:\n{str(e)}")
        
        finally:
            # 重新启用按钮
            self.reset_button.config(state=tk.NORMAL)
    
    def reset_cursor_app_data(self):
        """重置CURSOR应用数据"""
        self.log("正在重置CURSOR应用数据...", "info")
        
        # CURSOR应用数据路径
        cursor_paths = [
            os.path.join(os.environ['APPDATA'], 'cursor'),
            os.path.join(os.environ['LOCALAPPDATA'], 'cursor'),
            os.path.join(os.environ['LOCALAPPDATA'], 'Programs', 'cursor')
        ]
        
        for path in cursor_paths:
            if os.path.exists(path):
                try:
                    # 备份CURSOR数据
                    if self.backup_var.get():
                        backup_path = os.path.join(tempfile.gettempdir(), f"cursor_backup_{random.randint(1000, 9999)}")
                        shutil.copytree(path, backup_path)
                        self.log(f"CURSOR数据已备份到: {backup_path}", "info")
                    
                    # 查找并删除包含设备ID的文件
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            if file.endswith('.json'):
                                try:
                                    file_path = os.path.join(root, file)
                                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                        content = f.read()
                                        if 'deviceId' in content or 'machineId' in content or 'installationID' in content:
                                            self.log(f"找到设备ID文件: {file_path}", "info")
                                            os.remove(file_path)
                                            self.log(f"已删除: {file_path}", "success")
                                except Exception as e:
                                    self.log(f"处理文件时出错: {e}", "error")
                    
                    self.log(f"CURSOR数据重置完成: {path}", "success")
                except Exception as e:
                    self.log(f"重置CURSOR数据时出错: {e}", "error")
    
    def reset_cursor_browser_storage(self):
        """重置浏览器中CURSOR相关的存储"""
        self.log("正在重置浏览器中的CURSOR存储...", "info")
        
        # Chrome本地存储路径
        chrome_storage_paths = [
            os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'Local Storage', 'leveldb'),
            os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'Session Storage'),
            os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'IndexedDB')
        ]
        
        # Edge本地存储路径
        edge_storage_paths = [
            os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'Local Storage', 'leveldb'),
            os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'Session Storage'),
            os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'IndexedDB')
        ]
        
        # 清除Chrome中的CURSOR数据
        for path in chrome_storage_paths:
            if os.path.exists(path):
                try:
                    # 查找包含cursor相关域名的存储文件
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'rb') as f:
                                    content = f.read()
                                    if b'cursor.sh' in content or b'cursor.so' in content:
                                        self.log(f"找到CURSOR浏览器存储: {file_path}", "info")
                                        # 不直接删除文件，而是清除其中的CURSOR相关内容
                                        if self.backup_var.get():
                                            os.rename(file_path, file_path + '.bak')
                                            self.log(f"已备份: {file_path}", "success")
                                        else:
                                            os.remove(file_path)
                                            self.log(f"已删除: {file_path}", "success")
                            except Exception:
                                pass
                except Exception as e:
                    self.log(f"处理Chrome存储时出错: {e}", "error")
        
        # 清除Edge中的CURSOR数据
        for path in edge_storage_paths:
            if os.path.exists(path):
                try:
                    # 查找包含cursor相关域名的存储文件
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'rb') as f:
                                    content = f.read()
                                    if b'cursor.sh' in content or b'cursor.so' in content:
                                        self.log(f"找到CURSOR浏览器存储: {file_path}", "info")
                                        # 不直接删除文件，而是清除其中的CURSOR相关内容
                                        if self.backup_var.get():
                                            os.rename(file_path, file_path + '.bak')
                                            self.log(f"已备份: {file_path}", "success")
                                        else:
                                            os.remove(file_path)
                                            self.log(f"已删除: {file_path}", "success")
                            except Exception:
                                pass
                except Exception as e:
                    self.log(f"处理Edge存储时出错: {e}", "error")
        
        self.log("浏览器中的CURSOR存储重置完成", "success")
    
    def clear_cursor_cookies(self):
        """清除CURSOR相关的Cookie"""
        self.log("正在清除CURSOR相关的Cookie...", "info")
        
        try:
            # CURSOR相关的域名
            cursor_domains = ["cursor.sh", "cursor.so", "api.cursor.sh", "api.cursor.so"]
            
            # 浏览器Cookie和存储路径
            browser_paths = {
                "Chrome": {
                    "cookies": os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'Network', 'Cookies'),
                    "storage": [
                        os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'Local Storage', 'leveldb'),
                        os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data', 'Default', 'Session Storage')
                    ]
                },
                "Edge": {
                    "cookies": os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'Network', 'Cookies'),
                    "storage": [
                        os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'Local Storage', 'leveldb'),
                        os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft', 'Edge', 'User Data', 'Default', 'Session Storage')
                    ]
                }
            }
            
            # 处理每个浏览器
            for browser_name, paths in browser_paths.items():
                # 处理Cookie
                if os.path.exists(paths["cookies"]):
                    self.log(f"找到 {browser_name} 的Cookie文件", "info")
                    
                    # 备份Cookie文件
                    if self.backup_var.get():
                        backup_path = paths["cookies"] + '.bak'
                        try:
                            shutil.copy2(paths["cookies"], backup_path)
                            self.log(f"已备份Cookie文件: {backup_path}", "success")
                        except Exception as e:
                            self.log(f"备份Cookie文件失败: {e}", "error")
            
                # 处理存储
                for storage_path in paths["storage"]:
                    if os.path.exists(storage_path):
                        self.log(f"找到 {browser_name} 的存储: {storage_path}", "info")
                        
                        # 查找包含cursor相关域名的存储文件
                        for root, dirs, files in os.walk(storage_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                try:
                                    with open(file_path, 'rb') as f:
                                        content = f.read()
                                        if any(domain.encode() in content for domain in cursor_domains):
                                            if self.backup_var.get():
                                                shutil.copy2(file_path, file_path + '.bak')
                                                self.log(f"已备份存储文件: {file_path}", "success")
                                            
                                            os.remove(file_path)
                                            self.log(f"已删除存储文件: {file_path}", "success")
                                except Exception:
                                    pass  # 忽略无法读取的文件
            
            # 使用命令行清除浏览器数据
            if self.clear_all_browser_data_var.get():
                self.log("正在清除所有浏览器数据...", "info")
                
                # 清除Chrome数据
                chrome_cmd = f'start chrome --purge-memory-button'
                subprocess.run(chrome_cmd, shell=True, check=False)
                
                # 清除Edge数据
                edge_cmd = f'start msedge --purge-memory-button'
                subprocess.run(edge_cmd, shell=True, check=False)
                
                self.log("已发送清除浏览器数据命令", "success")
            
            self.log("Cookie清除操作完成", "success")
        except Exception as e:
            self.log(f"清除Cookie时出错: {e}", "error")
    
    def modify_cursor_registry(self):
        """修改注册表中CURSOR相关的项"""
        self.log("正在修改注册表中CURSOR相关的项...", "info")
        
        try:
            # CURSOR可能使用的注册表路径
            cursor_reg_paths = [
                r"SOFTWARE\Cursor",
                r"SOFTWARE\Classes\cursor",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Cursor"
            ]
            
            for path in cursor_reg_paths:
                try:
                    # 尝试打开注册表项
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, path, 0, winreg.KEY_ALL_ACCESS) as key:
                        # 查找可能包含设备ID的值
                        i = 0
                        while True:
                            try:
                                name, value, type = winreg.EnumValue(key, i)
                                if 'id' in name.lower() or 'device' in name.lower() or 'machine' in name.lower() or 'hardware' in name.lower():
                                    # 生成新的随机值
                                    new_value = str(uuid.uuid4())
                                    winreg.SetValueEx(key, name, 0, type, new_value)
                                    self.log(f"已修改注册表值: {path}\\{name} = {new_value}", "success")
                                i += 1
                            except WindowsError:
                                break
                except WindowsError:
                    # 注册表项不存在，跳过
                    pass
            
            self.log("注册表修改完成", "success")
        except Exception as e:
            self.log(f"修改注册表时出错: {e}", "error")

    def reset_network_identity(self):
        """重置网络标识"""
        self.log("正在重置网络标识...", "info")
        
        try:
            # 重置网络适配器
            self.log("尝试重置网络适配器...", "info")
            
            # 使用netsh命令重置网络配置
            try:
                # 重置TCP/IP堆栈
                subprocess.run("netsh int ip reset", shell=True, check=False)
                self.log("已重置TCP/IP堆栈", "success")
                
                # 重置Winsock目录
                subprocess.run("netsh winsock reset", shell=True, check=False)
                self.log("已重置Winsock目录", "success")
                
                # 刷新DNS缓存
                subprocess.run("ipconfig /flushdns", shell=True, check=False)
                self.log("已刷新DNS缓存", "success")
                
                # 释放并更新IP地址
                subprocess.run("ipconfig /release", shell=True, check=False)
                subprocess.run("ipconfig /renew", shell=True, check=False)
                self.log("已释放并更新IP地址", "success")
            except Exception as e:
                self.log(f"重置网络配置时出错: {e}", "error")
            
            # 修改注册表中的网络相关项
            try:
                # 生成新的网络ID
                new_network_id = str(uuid.uuid4())
                
                # 修改网络配置文件中的ID
                network_paths = [
                    r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\NetworkList\Profiles",
                    r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\NetworkList\Signatures\Unmanaged",
                    r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters",
                    r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
                ]
                
                for path in network_paths:
                    try:
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path, 0, winreg.KEY_READ) as key:
                            i = 0
                            while True:
                                try:
                                    subkey_name = winreg.EnumKey(key, i)
                                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, f"{path}\\{subkey_name}", 0, winreg.KEY_ALL_ACCESS) as subkey:
                                        # 修改各种网络标识符
                                        try:
                                            if self.use_random_hwid_var.get():
                                                for value_name in ["ProfileGuid", "Description", "DhcpHostname", "HostName"]:
                                                    try:
                                                        winreg.SetValueEx(subkey, value_name, 0, winreg.REG_SZ, f"PC-{new_network_id[:8]}")
                                                        self.log(f"已修改网络标识: {path}\\{subkey_name}\\{value_name}", "success")
                                                    except WindowsError:
                                                        pass
                                        except WindowsError:
                                            pass
                                    i += 1
                                except WindowsError:
                                    break
                    except WindowsError:
                        self.log(f"无法访问网络配置注册表: {path}", "warning")
                
                # 修改计算机名称相关注册表
                try:
                    computer_name_paths = [
                        r"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName",
                        r"SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName"
                    ]
                    
                    new_computer_name = f"PC-{new_network_id[:8]}"
                    
                    for path in computer_name_paths:
                        try:
                            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path, 0, winreg.KEY_ALL_ACCESS) as key:
                                winreg.SetValueEx(key, "ComputerName", 0, winreg.REG_SZ, new_computer_name)
                                self.log(f"已修改计算机名称: {path}\\ComputerName = {new_computer_name}", "success")
                        except WindowsError as e:
                            self.log(f"修改计算机名称时出错: {e}", "error")
                except Exception as e:
                    self.log(f"修改计算机名称时出错: {e}", "error")
                
                self.log("网络标识重置完成", "success")
            except Exception as e:
                self.log(f"修改网络标识时出错: {e}", "error")
        except Exception as e:
            self.log(f"重置网络标识时出错: {e}", "error")

    def terminate_cursor_process(self):
        """终止CURSOR进程"""
        self.log("正在终止CURSOR进程...", "info")
        
        try:
            # 使用taskkill命令终止所有可能的CURSOR进程
            cursor_processes = [
                "cursor.exe", 
                "Cursor.exe", 
                "cursor-updater.exe",
                "cursor-node.exe",
                "cursor-gpu-helper.exe",
                "cursor-crash-handler.exe"
            ]
            
            for process in cursor_processes:
                subprocess.run(f"taskkill /f /im {process}", shell=True, check=False)
            
            self.log("已终止CURSOR进程", "success")
            
            # 等待进程完全终止
            time.sleep(1)
        except Exception as e:
            self.log(f"终止CURSOR进程时出错: {e}", "error")

def main():
    """主函数"""
    try:
        # 检查是否有管理员权限
        if not ctypes.windll.shell32.IsUserAnAdmin():
            # 如果没有管理员权限，尝试以管理员身份重新运行
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
            return  # 退出当前非管理员实例
        
        # 如果有管理员权限，继续运行程序
        root = tk.Tk()
        app = CursorResetApp(root)
        root.mainloop()
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        # 防止窗口立即关闭
        input("程序出错，按Enter键退出...")

if __name__ == "__main__":
    main()




