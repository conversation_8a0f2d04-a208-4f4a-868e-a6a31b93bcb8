# -*- coding: utf-8 -*-
"""
可比公司模块 - 同花顺股票行业信息爬取与导入工具
功能：
1. 爬取指定上市日期范围内的股票行业信息
2. 将爬取的数据导入到数据库，更新"可比行业"字段
"""

import os
import sys
import time
import json
import requests
import pandas as pd
import sqlite3
import threading
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                            QPushButton, QTextEdit, QProgressBar, QMessageBox, 
                            QFileDialog, QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QObject

# 工作线程信号
class WorkerSignals(QObject):
    """工作线程信号"""
    log = pyqtSignal(str)
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)

# 爬取线程
class CrawlThread(QThread):
    """爬取数据线程"""
    def __init__(self, days, batch_size, interval):
        super().__init__()
        self.days = days
        self.batch_size = batch_size
        self.interval = interval
        self.signals = WorkerSignals()
        self.stocks_data = []
    
    def run(self):
        try:
            self.signals.log.emit(f"开始爬取最近 {self.days} 天上市的股票行业信息")
            
            # 获取股票列表
            stock_list = self.get_recent_stocks(self.days)
            if not stock_list:
                self.signals.log.emit("获取股票列表失败")
                self.signals.error.emit("获取股票列表失败")
                return
            
            self.signals.log.emit(f"获取到 {len(stock_list)} 只股票")
            
            # 批量获取行业信息
            results = self.get_all_stock_industries(stock_list, self.batch_size, self.interval)
            
            if results:
                self.stocks_data = results
                self.signals.log.emit(f"爬取完成，共获取 {len(results)} 只股票的行业信息")
                self.signals.finished.emit()
            else:
                self.signals.log.emit("未获取到任何股票的行业信息")
                self.signals.error.emit("未获取到任何股票的行业信息")
        
        except Exception as e:
            self.signals.log.emit(f"爬取过程中出错: {e}")
            self.signals.error.emit(str(e))
    
    def get_tushare_token(self):
        """读取配置文件中的Tushare API token"""
        try:
            import configparser
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            
            # 先检查[Tushare]部分
            if 'Tushare' in config and 'token' in config['Tushare']:
                return config['Tushare']['token']
            
            # 再检查[tushare]部分（小写）
            if 'tushare' in config and 'token' in config['tushare']:
                return config['tushare']['token']
            
            # 最后检查[API]部分
            if 'API' in config and 'token' in config['API']:
                return config['API']['token']
            
            return None
        except Exception:
            return None
    
    def get_recent_stocks(self, days):
        """获取最近指定天数上市的股票"""
        try:
            self.signals.log.emit("正在获取股票列表...")
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 格式化日期
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
            
            # 尝试使用Tushare API获取
            try:
                import tushare as ts
                
                # 读取配置文件中的Token
                token = self.get_tushare_token()
                if token:
                    self.signals.log.emit("使用Tushare API获取股票列表")
                    ts.set_token(token)
                    pro = ts.pro_api()
                    
                    # 获取所有上市股票列表
                    df = pro.stock_basic(exchange='', list_status='L', 
                                        fields='ts_code,symbol,name,list_date')
                    
                    # 筛选出指定日期范围内上市的股票
                    df = df[df['list_date'] >= start_date_str]
                    df = df[df['list_date'] <= end_date_str]
                    
                    if len(df) > 0:
                        self.signals.log.emit(f"通过Tushare API获取到 {len(df)} 只股票")
                        return df.to_dict('records')
                    else:
                        self.signals.log.emit("未找到符合条件的股票，将使用备用方法")
                else:
                    self.signals.log.emit("未找到Tushare API token，将使用备用方法")
            
            except Exception as e:
                self.signals.log.emit(f"使用Tushare API获取股票列表失败: {e}")
            
            self.signals.log.emit("使用备用方法获取股票列表...")
            
            # 使用硬编码的股票列表（沪深300+中证500+创业板50的主要成分股）
            stocks = [
                # 沪市主要股票
                {'symbol': '600000', 'name': '浦发银行'},
                {'symbol': '600036', 'name': '招商银行'},
                {'symbol': '600519', 'name': '贵州茅台'},
                {'symbol': '601318', 'name': '中国平安'},
                {'symbol': '600276', 'name': '恒瑞医药'},
                {'symbol': '600887', 'name': '伊利股份'},
                {'symbol': '601166', 'name': '兴业银行'},
                {'symbol': '600030', 'name': '中信证券'},
                {'symbol': '600031', 'name': '三一重工'},
                {'symbol': '600009', 'name': '上海机场'},
                {'symbol': '600309', 'name': '万华化学'},
                {'symbol': '601888', 'name': '中国中免'},
                {'symbol': '600585', 'name': '海螺水泥'},
                {'symbol': '601012', 'name': '隆基绿能'},
                {'symbol': '601688', 'name': '华泰证券'},
                {'symbol': '600050', 'name': '中国联通'},
                {'symbol': '601398', 'name': '工商银行'},
                {'symbol': '601288', 'name': '农业银行'},
                {'symbol': '601857', 'name': '中国石油'},
                {'symbol': '601988', 'name': '中国银行'},
                
                # 深市主要股票
                {'symbol': '000001', 'name': '平安银行'},
                {'symbol': '000333', 'name': '美的集团'},
                {'symbol': '000651', 'name': '格力电器'},
                {'symbol': '000858', 'name': '五粮液'},
                {'symbol': '000002', 'name': '万科A'},
                {'symbol': '000063', 'name': '中兴通讯'},
                {'symbol': '000725', 'name': '京东方A'},
                {'symbol': '000100', 'name': 'TCL科技'},
                {'symbol': '002415', 'name': '海康威视'},
                {'symbol': '002594', 'name': '比亚迪'},
                {'symbol': '002714', 'name': '牧原股份'},
                {'symbol': '002475', 'name': '立讯精密'},
                
                # 创业板主要股票
                {'symbol': '300750', 'name': '宁德时代'},
                {'symbol': '300059', 'name': '东方财富'},
                {'symbol': '300760', 'name': '迈瑞医疗'},
                {'symbol': '300015', 'name': '爱尔眼科'},
                {'symbol': '300122', 'name': '智飞生物'},
                {'symbol': '300124', 'name': '汇川技术'},
                {'symbol': '300014', 'name': '亿纳锡能'},
                {'symbol': '300347', 'name': '泰格医药'},
                
                # 科创板主要股票
                {'symbol': '688981', 'name': '中芯国际'},
                {'symbol': '688111', 'name': '金山办公'},
                {'symbol': '688012', 'name': '中微公司'},
                {'symbol': '688036', 'name': '传音控股'},
                {'symbol': '688561', 'name': '奇安信'},
                {'symbol': '688126', 'name': '沪硅产业'}
            ]
            
            # 转换为DataFrame
            df = pd.DataFrame(stocks)
            self.signals.log.emit(f"已准备 {len(df)} 只主要A股股票的列表")
            return df.to_dict('records')
        
        except Exception as e:
            self.signals.log.emit(f"获取股票列表时出错: {e}")
            return []
    
    def get_stock_industry(self, stock_info):
        """获取股票行业信息
        
        Args:
            stock_info: 股票信息，可以是字典或股票代码字符串
            
        Returns:
            dict: 包含股票代码和行业信息的字典
        """
        # 如果是字典，先提取相关信息
        if isinstance(stock_info, dict):
            # 如果已经有行业信息，直接返回
            if 'industry' in stock_info and stock_info['industry']:
                return {
                    'stock_code': stock_info.get('symbol', stock_info.get('ts_code', '')),
                    'name': stock_info.get('name', ''),
                    'industry': stock_info['industry']
                }
            
            # 提取股票代码
            if 'ts_code' in stock_info:
                stock_code = stock_info['ts_code'].split('.')[0]
            elif 'symbol' in stock_info:
                stock_code = stock_info['symbol']
            else:
                stock_code = str(stock_info)
            
            # 保存名称
            name = stock_info.get('name', '')
        else:
            # 如果是字符串，直接作为股票代码
            stock_code = str(stock_info)
            if '.' in stock_code:
                stock_code = stock_code.split('.')[0]
            name = ''
        
        # 从同花顺API获取行业信息
        url = f"http://basic.10jqka.com.cn/mapp/{stock_code}/a_companies_list.json"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.encoding = 'utf-8'
            data = response.json()
            
            # 提取行业信息
            industry = data['data']['field']
            return {
                'stock_code': stock_code,
                'name': name,
                'industry': industry
            }
        except Exception as e:
            self.signals.log.emit(f"获取股票 {stock_code} 行业信息失败: {e}")
            return None
    
    def get_all_stock_industries(self, stock_list, batch_size=10, interval=1):
        """批量获取股票行业信息
        
        Args:
            stock_list: 股票列表
            batch_size: 每批处理的股票数量
            interval: 每批之间的间隔时间(秒)
            
        Returns:
            list: 股票行业信息列表
        """
        total = len(stock_list)
        self.signals.log.emit(f"开始从同花顺API获取 {total} 只股票的行业信息...")
        
        # 强制从同花顺API获取行业信息，不使用Tushare API提供的行业数据
        results = []
        for i in range(0, total, batch_size):
            batch = stock_list[i:i+batch_size]
            self.signals.log.emit(f"正在处理第 {i+1}-{min(i+batch_size, total)} 只股票 (共 {total} 只)...")
            
            for stock in batch:
                # 获取股票代码和名称
                if isinstance(stock, dict):
                    if 'ts_code' in stock:
                        code = stock['ts_code'].split('.')[0]
                    elif 'symbol' in stock:
                        code = stock['symbol']
                    else:
                        code = str(stock)
                    name = stock.get('name', '')
                else:
                    code = str(stock)
                    name = ''
                
                # 从同花顺API获取行业信息
                url = f"http://basic.10jqka.com.cn/mapp/{code}/a_companies_list.json"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }
                
                try:
                    response = requests.get(url, headers=headers)
                    response.encoding = 'utf-8'
                    data = response.json()
                    
                    # 提取行业信息
                    industry = data['data']['field']
                    results.append({
                        'stock_code': code,
                        'name': name,
                        'industry': industry
                    })
                except Exception as e:
                    # 如果获取失败，打印错误信息
                    self.signals.log.emit(f"获取股票 {code} 的行业信息失败: {e}")
            
            # 显示进度
            progress = min(i+batch_size, total)
            self.signals.progress.emit(int(progress/total*100))
            self.signals.log.emit(f"已完成 {progress}/{total} ({progress/total*100:.1f}%)")
            
            # 每批处理完后等待一段时间，避免请求过于频繁
            if i + batch_size < total:
                time.sleep(interval)
        
        return results

# 导入数据库线程
class ImportThread(QThread):
    """导入数据库线程"""
    def __init__(self, stocks_data, db_path, table_name):
        super().__init__()
        self.stocks_data = stocks_data
        self.db_path = db_path
        self.table_name = table_name
        self.signals = WorkerSignals()
    
    def run(self):
        try:
            if not self.stocks_data:
                self.signals.log.emit("没有数据可导入")
                self.signals.error.emit("没有数据可导入")
                return
            
            # 添加数据库文件扩展名（如果没有）
            if not self.db_path.endswith('.db'):
                self.db_path = f"{self.db_path}.db"
            
            self.signals.log.emit(f"正在连接数据库: {self.db_path}")
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表结构信息
            cursor.execute(f"PRAGMA table_info({self.table_name})")
            table_info = cursor.fetchall()
            
            # 初始化计数器
            updated_count = 0
            inserted_count = 0
            skipped_count = 0
            
            # 检查表是否存在
            if not table_info:
                self.signals.log.emit(f"表 {self.table_name} 不存在，将创建新表")
                # 创建表
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    代码 TEXT PRIMARY KEY,
                    名称 TEXT,
                    可比行业 TEXT
                )
                """)
            else:
                # 打印表结构信息
                self.signals.log.emit(f"表 {self.table_name} 结构信息:")
                for col in table_info:
                    self.signals.log.emit(f"  {col[1]} ({col[2]})")
            
            # 遍历数据，逐行更新
            total = len(self.stocks_data)
            for idx, item in enumerate(self.stocks_data):
                stock_code = item['stock_code']
                industry = item['industry']
                
                # 检查记录是否已存在
                cursor.execute(f"SELECT * FROM {self.table_name} WHERE 代码 = ?", (stock_code,))
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 只更新可比行业字段，不影响其他字段
                    cursor.execute(f"""
                    UPDATE {self.table_name}
                    SET 可比行业 = ?
                    WHERE 代码 = ?
                    """, (industry, stock_code))
                    updated_count += 1
                else:
                    # 对于不存在的记录，跳过而不插入
                    self.signals.log.emit(f"跳过不存在的记录: {stock_code}")
                    skipped_count += 1
                
                # 更新进度
                self.signals.progress.emit(int((idx+1)/total*100))
            
            # 提交事务
            conn.commit()
            
            self.signals.log.emit(f"数据导入成功！更新: {updated_count} 条记录，跳过: {skipped_count} 条记录")
            self.signals.finished.emit()
        
        except Exception as e:
            self.signals.log.emit(f"导入数据库时出错: {e}")
            self.signals.error.emit(str(e))
        
        finally:
            # 关闭数据库连接
            if 'conn' in locals() and conn:
                conn.close()

# 导出Excel线程
class ExportThread(QThread):
    """导出Excel线程"""
    def __init__(self, stocks_data, output_file=None):
        super().__init__()
        self.stocks_data = stocks_data
        self.output_file = output_file
        self.signals = WorkerSignals()
    
    def run(self):
        try:
            if not self.stocks_data:
                self.signals.log.emit("没有数据可导出")
                self.signals.error.emit("没有数据可导出")
                return
            
            # 创建数据框
            df = pd.DataFrame(self.stocks_data)
            
            # 调整列名
            df.columns = ['股票代码', '股票名称', '行业']
            
            # 按股票代码排序
            df = df.sort_values(by=['股票代码'])
            
            # 生成文件名
            if not self.output_file:
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.output_file = f"股票行业信息_{current_time}.xlsx"
            
            # 保存到Excel
            df.to_excel(self.output_file, index=False)
            
            self.signals.log.emit(f"数据已导出到: {os.path.abspath(self.output_file)}")
            self.signals.finished.emit()
        
        except Exception as e:
            self.signals.log.emit(f"导出Excel时出错: {e}")
            self.signals.error.emit(str(e))

# 可比公司Widget
class ComparableCompaniesWidget(QWidget):
    """可比公司标签页Widget"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.stocks_data = []  # 存储爬取的股票数据
        self.db_path = "stock"  # 数据库路径
        self.table_name = "stocks"  # 表名
        self.is_crawling = False  # 是否正在爬取
        self.crawl_thread = None  # 爬取线程
        self.import_thread = None  # 导入线程
        self.export_thread = None  # 导出线程
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 参数设置区域
        param_group = QGroupBox("参数设置")
        param_layout = QFormLayout()
        
        # 上市天数输入
        self.days_input = QSpinBox()
        self.days_input.setRange(1, 3650)  # 1天到10年
        self.days_input.setValue(100)
        self.days_input.setToolTip("爬取最近多少天上市的股票")
        param_layout.addRow("最近上市天数:", self.days_input)
        
        # 批次大小输入
        self.batch_size_input = QSpinBox()
        self.batch_size_input.setRange(1, 100)
        self.batch_size_input.setValue(20)  # 默认值改为20
        self.batch_size_input.setToolTip("每批处理的股票数量")
        param_layout.addRow("每批处理数量:", self.batch_size_input)
        
        # 间隔时间输入
        self.interval_input = QDoubleSpinBox()
        self.interval_input.setRange(0.1, 10.0)
        self.interval_input.setValue(1.0)
        self.interval_input.setSingleStep(0.1)
        self.interval_input.setToolTip("每批之间的间隔时间(秒)")
        param_layout.addRow("请求间隔(秒):", self.interval_input)
        
        # 数据库设置
        self.db_path_input = QLineEdit("stock")
        self.db_path_input.setToolTip("数据库文件路径，不需要包含.db扩展名")
        param_layout.addRow("数据库路径:", self.db_path_input)
        
        self.table_name_input = QLineEdit("stocks")
        self.table_name_input.setToolTip("数据库表名")
        param_layout.addRow("表名:", self.table_name_input)
        
        param_group.setLayout(param_layout)
        main_layout.addWidget(param_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 开始爬取按钮
        self.crawl_button = QPushButton("开始爬取")
        self.crawl_button.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.crawl_button)
        
        # 导入数据库按钮
        self.import_button = QPushButton("导入数据库")
        self.import_button.clicked.connect(self.import_to_db)
        self.import_button.setEnabled(True)  # 默认启用
        button_layout.addWidget(self.import_button)
        
        # 导出Excel按钮
        self.export_button = QPushButton("导出Excel")
        self.export_button.clicked.connect(self.export_to_excel)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.export_button)
        
        main_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("日志信息")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
        # 添加初始日志
        self.add_log("可比公司模块已启动，请设置参数后开始爬取")
    
    def add_log(self, message):
        """添加日志消息"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{current_time}] {message}")
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum())
    
    def start_crawling(self):
        """开始爬取数据"""
        if self.is_crawling:
            self.add_log("已有爬取任务正在进行")
            return
        
        # 获取参数
        days = self.days_input.value()
        batch_size = self.batch_size_input.value()
        interval = self.interval_input.value()
        
        # 清空之前的数据
        self.stocks_data = []
        
        # 禁用按钮
        self.crawl_button.setEnabled(False)
        self.import_button.setEnabled(False)
        self.export_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 设置爬取状态
        self.is_crawling = True
        
        # 创建并启动爬取线程
        self.crawl_thread = CrawlThread(days, batch_size, interval)
        self.crawl_thread.signals.log.connect(self.add_log)
        self.crawl_thread.signals.progress.connect(self.progress_bar.setValue)
        self.crawl_thread.signals.finished.connect(self.crawling_finished)
        self.crawl_thread.signals.error.connect(self.crawling_error)
        self.crawl_thread.start()
    
    def crawling_finished(self):
        """爬取完成回调"""
        # 获取爬取的数据
        self.stocks_data = self.crawl_thread.stocks_data
        
        # 恢复状态
        self.is_crawling = False
        self.crawl_button.setEnabled(True)
        
        # 如果有数据，启用导出和导入按钮
        if self.stocks_data:
            self.import_button.setEnabled(True)
            self.export_button.setEnabled(True)
    
    def crawling_error(self, error_message):
        """爬取错误回调"""
        # 显示错误消息
        QMessageBox.critical(self, "爬取错误", f"爬取过程中出错: {error_message}")
        
        # 恢复状态
        self.is_crawling = False
        self.crawl_button.setEnabled(True)
    
    def import_to_db(self):
        """导入数据到数据库"""
        # 获取数据库参数
        self.db_path = self.db_path_input.text()
        self.table_name = self.table_name_input.text()
        
        # 如果没有爬取的数据，提示用户选择Excel文件
        if not self.stocks_data:
            # 选择Excel文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls);;All Files (*)"
            )
            
            if not file_path:
                return  # 用户取消了选择
            
            try:
                # 读取Excel文件
                self.add_log(f"正在读取Excel文件: {file_path}")
                df = pd.read_excel(file_path)
                
                # 检查必要的列是否存在
                required_columns = ['股票代码', '行业']
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if missing_columns:
                    error_msg = f"Excel文件缺少必要的列: {', '.join(missing_columns)}"
                    self.add_log(error_msg)
                    QMessageBox.critical(self, "错误", error_msg)
                    return
                
                # 转换为列表格式
                data_list = []
                for _, row in df.iterrows():
                    data_list.append({
                        'stock_code': str(row['股票代码']).zfill(6),
                        'name': row['股票名称'] if '股票名称' in df.columns else '',
                        'industry': row['行业']
                    })
                
                self.stocks_data = data_list
                self.add_log(f"成功读取Excel文件，共有 {len(data_list)} 条记录")
            
            except Exception as e:
                error_msg = f"读取Excel文件时出错: {e}"
                self.add_log(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
                return
        
        # 禁用按钮
        self.crawl_button.setEnabled(False)
        self.import_button.setEnabled(False)
        self.export_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 创建并启动导入线程
        self.import_thread = ImportThread(self.stocks_data, self.db_path, self.table_name)
        self.import_thread.signals.log.connect(self.add_log)
        self.import_thread.signals.progress.connect(self.progress_bar.setValue)
        self.import_thread.signals.finished.connect(self.import_finished)
        self.import_thread.signals.error.connect(self.import_error)
        self.import_thread.start()
    
    def import_finished(self):
        """导入完成回调"""
        # 恢复按钮状态
        self.crawl_button.setEnabled(True)
        self.import_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        # 显示成功消息
        QMessageBox.information(self, "导入成功", "数据已成功导入到数据库")
    
    def import_error(self, error_message):
        """导入错误回调"""
        # 显示错误消息
        QMessageBox.critical(self, "导入错误", f"导入数据库时出错: {error_message}")
        
        # 恢复按钮状态
        self.crawl_button.setEnabled(True)
        self.import_button.setEnabled(True)
        self.export_button.setEnabled(True)
    
    def export_to_excel(self):
        """导出数据到Excel"""
        if not self.stocks_data:
            self.add_log("没有数据可导出")
            return
        
        # 选择保存路径
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"股票行业信息_{current_time}.xlsx"
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", default_filename, "Excel Files (*.xlsx);;All Files (*)"
        )
        
        if not filename:
            return  # 用户取消了保存
        
        # 禁用按钮
        self.crawl_button.setEnabled(False)
        self.import_button.setEnabled(False)
        self.export_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 创建并启动导出线程
        self.export_thread = ExportThread(self.stocks_data, filename)
        self.export_thread.signals.log.connect(self.add_log)
        self.export_thread.signals.finished.connect(self.export_finished)
        self.export_thread.signals.error.connect(self.export_error)
        self.export_thread.start()
    
    def export_finished(self):
        """导出完成回调"""
        # 恢复按钮状态
        self.crawl_button.setEnabled(True)
        self.import_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        # 获取导出文件的完整路径
        output_path = os.path.abspath(self.export_thread.output_file)
        
        # 显示成功消息，包含文件路径
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("导出成功")
        msg_box.setText(f"数据已成功导出到:\n{output_path}")
        msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Open)
        msg_box.button(QMessageBox.Open).setText("打开文件夹")
        
        # 如果用户点击“打开文件夹”按钮
        if msg_box.exec_() == QMessageBox.Open:
            # 打开文件所在文件夹
            folder_path = os.path.dirname(output_path)
            os.startfile(folder_path)
    
    def export_error(self, error_message):
        """导出错误回调"""
        # 显示错误消息
        QMessageBox.critical(self, "导出错误", f"导出Excel时出错: {error_message}")
        
        # 恢复按钮状态
        self.crawl_button.setEnabled(True)
        self.import_button.setEnabled(True)
        self.export_button.setEnabled(True)
