"""
打包工具 - 用于将程序打包成可执行文件
支持选择存储目录、选择图标，并记忆之前的选择路径
"""
import os
import sys
import json
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import threading
import configparser

# 配置文件路径
CONFIG_FILE = "config.ini"

class PackageTool:
    def __init__(self, root):
        self.root = root
        self.root.title("程序打包工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 默认配置
        self.default_config = {
            "output_dir": os.path.join(os.path.dirname(os.path.abspath(__file__)), "dist"),
            "icon_path": "",
            "python_path": sys.executable,
            "main_script": "start.py",
            "onefile": False,
            "console": False,
            "use_spec": True,
            "spec_file": "start.spec"
        }
        
        # 加载配置
        self.config = self.load_config()
        
        # 创建界面
        self.create_widgets()
    
    def load_config(self):
        """加载配置文件"""
        config = self.default_config.copy()
        if os.path.exists(CONFIG_FILE):
            try:
                # 使用configparser读取INI文件
                cp = configparser.ConfigParser()
                cp.read(CONFIG_FILE, encoding="utf-8")
                
                # 检查是否存在Package部分
                if "Package" in cp:
                    # 遍历默认配置中的所有键，尝试从配置文件中读取
                    for key in self.default_config.keys():
                        if key in cp["Package"]:
                            # 根据默认值的类型进行转换
                            if isinstance(self.default_config[key], bool):
                                config[key] = cp["Package"].getboolean(key)
                            elif isinstance(self.default_config[key], int):
                                config[key] = cp["Package"].getint(key)
                            elif isinstance(self.default_config[key], float):
                                config[key] = cp["Package"].getfloat(key)
                            else:
                                config[key] = cp["Package"].get(key)
                return config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return config
        return config
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 使用configparser写入INI文件
            cp = configparser.ConfigParser()
            
            # 先读取现有的配置文件，保留其他部分
            if os.path.exists(CONFIG_FILE):
                cp.read(CONFIG_FILE, encoding="utf-8")
            
            # 确保存在Package部分
            if "Package" not in cp:
                cp["Package"] = {}
            
            # 将配置写入Package部分
            for key, value in self.config.items():
                cp["Package"][key] = str(value)
            
            # 保存到文件
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                cp.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.output_dir_var = tk.StringVar(value=self.config["output_dir"])
        output_entry = ttk.Entry(main_frame, textvariable=self.output_dir_var, width=50)
        output_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.select_output_dir).grid(row=0, column=2, padx=5, pady=5)
        
        # 图标选择
        ttk.Label(main_frame, text="图标文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.icon_path_var = tk.StringVar(value=self.config["icon_path"])
        icon_entry = ttk.Entry(main_frame, textvariable=self.icon_path_var, width=50)
        icon_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.select_icon).grid(row=1, column=2, padx=5, pady=5)
        
        # Python解释器路径
        ttk.Label(main_frame, text="Python路径:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.python_path_var = tk.StringVar(value=self.config["python_path"])
        python_entry = ttk.Entry(main_frame, textvariable=self.python_path_var, width=50)
        python_entry.grid(row=2, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.select_python_path).grid(row=2, column=2, padx=5, pady=5)
        
        # 主脚本文件
        ttk.Label(main_frame, text="主脚本文件:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.main_script_var = tk.StringVar(value=self.config["main_script"])
        main_script_entry = ttk.Entry(main_frame, textvariable=self.main_script_var, width=50)
        main_script_entry.grid(row=3, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.select_main_script).grid(row=3, column=2, padx=5, pady=5)
        
        # 打包选项
        options_frame = ttk.LabelFrame(main_frame, text="打包选项")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        # 单文件选项
        self.onefile_var = tk.BooleanVar(value=self.config["onefile"])
        ttk.Checkbutton(options_frame, text="单文件模式", variable=self.onefile_var).grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        
        # 控制台选项
        self.console_var = tk.BooleanVar(value=self.config["console"])
        ttk.Checkbutton(options_frame, text="显示控制台", variable=self.console_var).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 使用spec文件选项
        self.use_spec_var = tk.BooleanVar(value=self.config["use_spec"])
        ttk.Checkbutton(options_frame, text="使用spec文件", variable=self.use_spec_var, command=self.toggle_spec_file).grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        
        # Spec文件选择
        ttk.Label(options_frame, text="Spec文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.spec_file_var = tk.StringVar(value=self.config["spec_file"])
        self.spec_entry = ttk.Entry(options_frame, textvariable=self.spec_file_var, width=40)
        self.spec_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5)
        self.spec_browse_button = ttk.Button(options_frame, text="浏览...", command=self.select_spec_file)
        self.spec_browse_button.grid(row=1, column=2, padx=5, pady=5)
        
        # 根据是否使用spec文件设置状态
        self.toggle_spec_file()
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress.grid(row=5, column=0, columnspan=3, sticky=tk.W+tk.E, pady=10)
        
        # 日志区域
        ttk.Label(main_frame, text="打包日志:").grid(row=6, column=0, sticky=tk.W)
        self.log_text = tk.Text(main_frame, height=10, width=70, wrap=tk.WORD)
        self.log_text.grid(row=7, column=0, columnspan=3, sticky=tk.W+tk.E+tk.N+tk.S)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, command=self.log_text.yview)
        scrollbar.grid(row=7, column=3, sticky=tk.N+tk.S)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="开始打包", command=self.start_packaging).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置", command=self.save_current_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)
        
        # 帮助说明
        help_text = """使用说明:
1. 如果选择"使用spec文件"，将使用已有的spec文件进行打包，这种方式更可靠
2. 如果不选择"使用spec文件"，将直接使用PyInstaller命令打包
3. Python路径是Python解释器的可执行文件路径，通常不需要修改"""
        help_label = ttk.Label(main_frame, text=help_text, wraplength=580, justify=tk.LEFT)
        help_label.grid(row=9, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # 设置列权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
    
    def toggle_spec_file(self):
        """根据是否使用spec文件切换相关控件的状态"""
        if self.use_spec_var.get():
            self.spec_entry.config(state="normal")
            self.spec_browse_button.config(state="normal")
        else:
            self.spec_entry.config(state="disabled")
            self.spec_browse_button.config(state="disabled")
    
    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)
    
    def select_icon(self):
        """选择图标文件"""
        filetypes = [("图标文件", "*.ico"), ("所有文件", "*.*")]
        filename = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.icon_path_var.get()) if self.icon_path_var.get() else os.getcwd(),
            filetypes=filetypes
        )
        if filename:
            self.icon_path_var.set(filename)
    
    def select_main_script(self):
        """选择主脚本文件"""
        filetypes = [("Python文件", "*.py"), ("所有文件", "*.*")]
        filename = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.main_script_var.get()) if self.main_script_var.get() else os.getcwd(),
            filetypes=filetypes
        )
        if filename:
            self.main_script_var.set(filename)
    
    def select_spec_file(self):
        """选择spec文件"""
        filetypes = [("Spec文件", "*.spec"), ("所有文件", "*.*")]
        filename = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.spec_file_var.get()) if self.spec_file_var.get() else os.getcwd(),
            filetypes=filetypes
        )
        if filename:
            self.spec_file_var.set(filename)
    
    def select_python_path(self):
        """选择Python解释器路径"""
        filetypes = [("可执行文件", "*.exe"), ("所有文件", "*.*")]
        filename = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.python_path_var.get()) if self.python_path_var.get() else os.getcwd(),
            filetypes=filetypes
        )
        if filename:
            self.python_path_var.set(filename)
    
    def save_current_config(self):
        """保存当前配置"""
        self.config["output_dir"] = self.output_dir_var.get()
        self.config["icon_path"] = self.icon_path_var.get()
        self.config["python_path"] = self.python_path_var.get()
        self.config["main_script"] = self.main_script_var.get()
        self.config["onefile"] = self.onefile_var.get()
        self.config["console"] = self.console_var.get()
        self.config["use_spec"] = self.use_spec_var.get()
        self.config["spec_file"] = self.spec_file_var.get()
        self.save_config()
        messagebox.showinfo("保存成功", "配置已保存")
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def create_direct_command(self):
        """创建直接打包的命令"""
        cmd = [self.python_path_var.get(), "-m", "PyInstaller"]
        
        # 添加图标
        icon_path = self.icon_path_var.get()
        if icon_path and os.path.exists(icon_path):
            cmd.extend(["--icon", icon_path])
        
        # 添加输出目录
        output_dir = self.output_dir_var.get()
        if output_dir:
            cmd.extend(["--distpath", output_dir])
        
        # 添加打包选项
        if self.onefile_var.get():
            cmd.append("--onefile")
        else:
            cmd.append("--onedir")
        
        if not self.console_var.get():
            cmd.append("--noconsole")
        
        # 添加数据文件
        cmd.extend([
            "--add-data", "config.ini;.",
            "--add-data", "*.db;.",
            "--add-data", "data/*.py;data",
            "--add-data", "api/*.py;api",
            "--add-data", "ui/*.py;ui",
            "--add-data", "utils/*.py;utils"
        ])
        
        # 添加隐式导入
        cmd.extend([
            "--hidden-import", "pandas",
            "--hidden-import", "numpy",
            "--hidden-import", "tkinter",
            "--hidden-import", "sqlite3",
            "--hidden-import", "tushare"
        ])
        
        # 添加-y参数以确认覆盖
        cmd.append("-y")
        
        # 添加主脚本
        cmd.append(self.main_script_var.get())
        
        return cmd
    
    def create_spec_command(self):
        """创建使用spec文件打包的命令"""
        cmd = [self.python_path_var.get(), "-m", "PyInstaller"]
        
        # 添加输出目录
        output_dir = self.output_dir_var.get()
        if output_dir:
            cmd.extend(["--distpath", output_dir])
        
        # 添加spec文件
        cmd.append(self.spec_file_var.get())
        
        # 添加-y参数以确认覆盖
        cmd.append("-y")
        
        return cmd
    
    def start_packaging(self):
        """开始打包"""
        # 保存当前配置
        self.save_current_config()
        
        # 创建输出目录
        output_dir = self.output_dir_var.get()
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                self.log(f"创建输出目录失败: {e}")
                return
        
        # 启动打包线程
        threading.Thread(target=self.run_packaging, daemon=True).start()
    
    def run_packaging(self):
        """运行打包进程"""
        self.log("开始打包...")
        self.progress_var.set(10)
        
        # 根据是否使用spec文件选择打包方式
        if self.use_spec_var.get():
            # 检查spec文件是否存在
            spec_file = self.spec_file_var.get()
            if not os.path.exists(spec_file):
                self.log(f"错误: spec文件不存在: {spec_file}")
                messagebox.showerror("错误", f"spec文件不存在: {spec_file}")
                self.progress_var.set(0)
                return
            
            # 使用spec文件打包
            cmd = self.create_spec_command()
            self.log("使用spec文件打包...")
        else:
            # 直接打包
            cmd = self.create_direct_command()
            self.log("直接使用PyInstaller命令打包...")
        
        try:
            # 运行打包命令
            self.log(f"执行命令: {' '.join(cmd)}")
            self.progress_var.set(20)
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding="utf-8",
                errors="replace"
            )
            
            # 实时读取输出
            for line in process.stdout:
                self.log(line.strip())
                # 根据输出更新进度条
                if "Building PKG" in line:
                    self.progress_var.set(50)
                elif "Building EXE" in line:
                    self.progress_var.set(70)
                elif "Building COLLECT" in line:
                    self.progress_var.set(90)
            
            # 等待进程结束
            return_code = process.wait()
            self.progress_var.set(100)
            
            # 获取输出目录
            output_dir = self.output_dir_var.get()
            
            if return_code == 0:
                self.log("打包成功!")
                self.log(f"输出目录: {output_dir}")
                messagebox.showinfo("打包成功", f"程序已成功打包!\n输出目录: {output_dir}")
            else:
                self.log(f"打包失败，返回代码: {return_code}")
                messagebox.showerror("打包失败", f"打包过程中出现错误，返回代码: {return_code}")
        
        except Exception as e:
            self.log(f"打包过程中发生错误: {e}")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"打包过程中发生错误: {e}")

def main():
    root = tk.Tk()
    app = PackageTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()