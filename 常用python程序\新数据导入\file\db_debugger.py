import os
import sqlite3
import pandas as pd
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QLineEdit, QTextEdit, QGroupBox, QMessageBox, QTableWidget,
                           QTableWidgetItem, QHeaderView, QComboBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QObject

class DbDebuggerSignals(QObject):
    """
    定义数据库调试器的信号
    """
    update_log = pyqtSignal(str)
    update_result = pyqtSignal(pd.DataFrame)
    error = pyqtSignal(str)

class DbDebuggerWidget(QWidget):
    """数据库调试界面，用于诊断数据库查询问题"""

    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建标题
        title_label = QLabel('数据库调试工具')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建数据库设置区域
        db_group = QGroupBox('数据库设置')
        db_layout = QHBoxLayout()

        self.db_path_label = QLabel('数据库文件:')
        self.db_path_edit = QLineEdit('stock.db')
        # 添加回车响应
        self.db_path_edit.returnPressed.connect(self.load_tables)

        self.browse_button = QPushButton('浏览...')
        self.browse_button.clicked.connect(self.browse_db_file)

        db_layout.addWidget(self.db_path_label)
        db_layout.addWidget(self.db_path_edit)
        db_layout.addWidget(self.browse_button)

        db_group.setLayout(db_layout)
        main_layout.addWidget(db_group)

        # 创建查询设置区域
        query_group = QGroupBox('查询设置')
        query_layout = QVBoxLayout()

        # 表名设置
        table_layout = QHBoxLayout()
        self.table_label = QLabel('表名:')
        self.table_edit = QLineEdit('stocks')
        # 添加回车响应
        self.table_edit.returnPressed.connect(self.execute_query)

        self.load_tables_button = QPushButton('加载表')
        self.load_tables_button.clicked.connect(self.load_tables)
        self.tables_combo = QComboBox()
        self.tables_combo.currentTextChanged.connect(self.on_table_changed)

        table_layout.addWidget(self.table_label)
        table_layout.addWidget(self.table_edit)
        table_layout.addWidget(self.load_tables_button)
        table_layout.addWidget(self.tables_combo)
        query_layout.addLayout(table_layout)

        # 查询条件设置
        condition_layout = QHBoxLayout()
        self.condition_label = QLabel('查询条件:')
        self.condition_edit = QLineEdit()
        self.condition_edit.setPlaceholderText('例如: 代码="600828" 或留空查询所有')
        # 添加回车响应
        self.condition_edit.returnPressed.connect(self.execute_query)

        condition_layout.addWidget(self.condition_label)
        condition_layout.addWidget(self.condition_edit)
        query_layout.addLayout(condition_layout)

        # 查询按钮
        buttons_layout = QHBoxLayout()
        self.query_button = QPushButton('执行查询')
        self.query_button.clicked.connect(self.execute_query)
        self.show_schema_button = QPushButton('显示表结构')
        self.show_schema_button.clicked.connect(self.show_table_schema)
        self.count_records_button = QPushButton('统计记录数')
        self.count_records_button.clicked.connect(self.count_records)

        buttons_layout.addWidget(self.query_button)
        buttons_layout.addWidget(self.show_schema_button)
        buttons_layout.addWidget(self.count_records_button)
        query_layout.addLayout(buttons_layout)

        query_group.setLayout(query_layout)
        main_layout.addWidget(query_group)

        # 创建SQL执行区域
        sql_group = QGroupBox('SQL执行')
        sql_layout = QVBoxLayout()

        self.sql_edit = QTextEdit()
        self.sql_edit.setPlaceholderText('输入SQL语句...')
        # 为QTextEdit添加快捷键
        from PyQt5.QtGui import QKeySequence
        from PyQt5.QtWidgets import QShortcut
        self.sql_shortcut = QShortcut(QKeySequence("Return"), self.sql_edit)
        self.sql_shortcut.activated.connect(self.execute_sql)
        # Ctrl+Enter 也可以执行
        self.sql_shortcut_ctrl = QShortcut(QKeySequence("Ctrl+Return"), self.sql_edit)
        self.sql_shortcut_ctrl.activated.connect(self.execute_sql)

        self.execute_sql_button = QPushButton('执行SQL')
        self.execute_sql_button.clicked.connect(self.execute_sql)

        sql_layout.addWidget(self.sql_edit)
        sql_layout.addWidget(self.execute_sql_button)
        sql_group.setLayout(sql_layout)
        main_layout.addWidget(sql_group)

        # 创建结果显示区域
        result_group = QGroupBox('查询结果')
        result_layout = QVBoxLayout()

        self.result_table = QTableWidget()
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        result_layout.addWidget(self.result_table)
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)

        # 创建日志区域
        log_group = QGroupBox('操作日志')
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置布局
        self.setLayout(main_layout)

        # 初始化信号
        self.signals = DbDebuggerSignals()
        self.signals.update_log.connect(self.update_log)
        self.signals.update_result.connect(self.update_result_table)
        self.signals.error.connect(self.show_error)

        # 添加初始日志
        self.update_log("数据库调试工具已启动，请设置数据库参数后执行查询")

    def update_log(self, message):
        """更新日志显示"""
        import time
        current_time = time.strftime('%H:%M:%S')
        log_message = f"[{current_time}] {message}"
        self.log_text.append(log_message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum())

    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, '错误', message)

    def update_result_table(self, df):
        """更新结果表格"""
        # 清空表格
        self.result_table.clear()
        self.result_table.setRowCount(0)
        self.result_table.setColumnCount(0)

        if df.empty:
            self.update_log("查询结果为空")
            return

        # 设置列数和列标题
        self.result_table.setColumnCount(len(df.columns))
        self.result_table.setHorizontalHeaderLabels(df.columns)

        # 添加数据行
        for i, row in df.iterrows():
            self.result_table.insertRow(i)
            for j, value in enumerate(row):
                self.result_table.setItem(i, j, QTableWidgetItem(str(value) if value is not None else "NULL"))

        self.update_log(f"查询返回 {len(df)} 条记录")

    def browse_db_file(self):
        """浏览选择数据库文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择数据库文件', self.db_path_edit.text(), 'SQLite数据库 (*.db);;所有文件 (*)')
        if file_path:
            self.db_path_edit.setText(file_path)
            self.update_log(f"已选择数据库文件: {file_path}")
            # 自动加载表
            self.load_tables()

    def load_tables(self):
        """加载数据库中的表"""
        db_path = self.db_path_edit.text().strip()
        if not db_path:
            self.show_error("请指定数据库文件路径")
            return

        if not os.path.exists(db_path):
            self.show_error(f"数据库文件不存在: {db_path}")
            return

        try:
            self.update_log(f"正在加载数据库 {db_path} 中的表...")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            conn.close()

            # 更新下拉框
            self.tables_combo.clear()
            for table in tables:
                self.tables_combo.addItem(table[0])

            self.update_log(f"成功加载 {len(tables)} 个表")

            # 如果有表，选择第一个
            if tables:
                self.table_edit.setText(tables[0][0])

        except Exception as e:
            self.show_error(f"加载表时出错: {str(e)}")

    def on_table_changed(self, table_name):
        """表名下拉框选择变化时的处理"""
        if table_name:
            self.table_edit.setText(table_name)
            self.update_log(f"已选择表: {table_name}")

    def execute_query(self):
        """执行查询"""
        db_path = self.db_path_edit.text().strip()
        table_name = self.table_edit.text().strip()
        condition = self.condition_edit.text().strip()

        if not db_path:
            self.show_error("请指定数据库文件路径")
            return

        if not table_name:
            self.show_error("请指定表名")
            return

        if not os.path.exists(db_path):
            self.show_error(f"数据库文件不存在: {db_path}")
            return

        try:
            self.update_log(f"正在查询表 {table_name}...")

            conn = sqlite3.connect(db_path)

            # 构建SQL查询
            sql = f"SELECT * FROM [{table_name}]"

            # 处理查询条件
            if condition:
                # 检查是否是股票代码输入
                if condition.isdigit() or (condition.startswith(('SH', 'SZ', 'BJ')) and condition[2:].isdigit()) or '.' in condition:
                    # 用户输入的是股票代码，构建多种可能的格式进行查询
                    possible_codes = [condition]  # 原始代码

                    # 如果是纯数字代码（如600001）
                    if condition.isdigit():
                        if condition.startswith('6'):
                            possible_codes.append(f"SH{condition}")  # 添加SH前缀
                            possible_codes.append(f"{condition}.SH")  # 添加.SH后缀
                        else:
                            possible_codes.append(f"SZ{condition}")  # 添加SZ前缀
                            possible_codes.append(f"{condition}.SZ")  # 添加.SZ后缀

                    # 如果是带前缀的代码（如SH600001）
                    elif (condition.upper().startswith('SH') or condition.upper().startswith('SZ')) and condition[2:].isdigit():
                        prefix = condition[:2].upper()
                        number_part = condition[2:]
                        possible_codes.append(number_part)  # 纯数字部分
                        possible_codes.append(f"{number_part}.{prefix}")  # 带后缀格式 (600001.SH)

                    # 如果是带后缀的代码（如600001.SH）
                    elif '.' in condition:
                        parts = condition.split('.')
                        number_part = parts[0]
                        suffix = parts[1].upper()
                        possible_codes.append(number_part)  # 纯数字部分
                        possible_codes.append(f"{suffix}{number_part}")  # 带前缀格式 (SH600001)

                    # 构建查询条件
                    code_conditions = " OR ".join([f"代码 = '{code}'" for code in possible_codes])
                    sql += f" WHERE ({code_conditions})"
                    self.update_log(f"检测到股票代码输入: {condition}")
                    self.update_log(f"尝试查询的代码格式: {', '.join(possible_codes)}")
                elif '=' not in condition and '>' not in condition and '<' not in condition and 'LIKE' not in condition.upper():
                    # 如果输入的不是标准SQL条件，尝试多种匹配方式
                    # 1. 尝试匹配代码
                    # 2. 尝试匹配名称（使用更灵活的模糊匹配）
                    sql += f" WHERE 代码 LIKE '%{condition}%' OR 名称 LIKE '%{condition}%'"
                    self.update_log(f"使用模糊匹配股票代码或名称: {condition}")
                else:
                    # 用户输入的是完整的SQL条件
                    sql += f" WHERE {condition}"

            self.update_log(f"执行SQL: {sql}")

            # 执行查询
            df = pd.read_sql_query(sql, conn)

            conn.close()

            # 更新结果表格
            self.signals.update_result.emit(df)

            # 显示查询结果数量
            if df.empty:
                self.update_log("查询结果为空")
            else:
                self.update_log(f"查询返回 {len(df)} 条记录")

        except Exception as e:
            self.show_error(f"执行查询时出错: {str(e)}")

    def show_table_schema(self):
        """显示表结构"""
        db_path = self.db_path_edit.text().strip()
        table_name = self.table_edit.text().strip()

        if not db_path:
            self.show_error("请指定数据库文件路径")
            return

        if not table_name:
            self.show_error("请指定表名")
            return

        if not os.path.exists(db_path):
            self.show_error(f"数据库文件不存在: {db_path}")
            return

        try:
            self.update_log(f"正在获取表 {table_name} 的结构...")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询表结构
            cursor.execute(f"PRAGMA table_info([{table_name}]);")
            columns = cursor.fetchall()

            conn.close()

            # 创建DataFrame
            df = pd.DataFrame(columns, columns=['cid', 'name', 'type', 'notnull', 'dflt_value', 'pk'])

            # 更新结果表格
            self.signals.update_result.emit(df)

            self.update_log(f"表 {table_name} 有 {len(columns)} 个字段")

        except Exception as e:
            self.show_error(f"获取表结构时出错: {str(e)}")

    def count_records(self):
        """统计记录数"""
        db_path = self.db_path_edit.text().strip()
        table_name = self.table_edit.text().strip()
        condition = self.condition_edit.text().strip()

        if not db_path:
            self.show_error("请指定数据库文件路径")
            return

        if not table_name:
            self.show_error("请指定表名")
            return

        if not os.path.exists(db_path):
            self.show_error(f"数据库文件不存在: {db_path}")
            return

        try:
            self.update_log(f"正在统计表 {table_name} 的记录数...")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 构建SQL查询
            sql = f"SELECT COUNT(*) FROM [{table_name}]"

            # 处理查询条件，与execute_query保持一致
            if condition:
                # 检查是否是股票代码输入
                if condition.isdigit() or (condition.startswith(('SH', 'SZ', 'BJ')) and condition[2:].isdigit()) or '.' in condition:
                    # 用户输入的是股票代码，构建多种可能的格式进行查询
                    possible_codes = [condition]  # 原始代码

                    # 如果是纯数字代码（如600001）
                    if condition.isdigit():
                        if condition.startswith('6'):
                            possible_codes.append(f"SH{condition}")  # 添加SH前缀
                            possible_codes.append(f"{condition}.SH")  # 添加.SH后缀
                        else:
                            possible_codes.append(f"SZ{condition}")  # 添加SZ前缀
                            possible_codes.append(f"{condition}.SZ")  # 添加.SZ后缀

                    # 如果是带前缀的代码（如SH600001）
                    elif (condition.upper().startswith('SH') or condition.upper().startswith('SZ')) and condition[2:].isdigit():
                        prefix = condition[:2].upper()
                        number_part = condition[2:]
                        possible_codes.append(number_part)  # 纯数字部分
                        possible_codes.append(f"{number_part}.{prefix}")  # 带后缀格式 (600001.SH)

                    # 如果是带后缀的代码（如600001.SH）
                    elif '.' in condition:
                        parts = condition.split('.')
                        number_part = parts[0]
                        suffix = parts[1].upper()
                        possible_codes.append(number_part)  # 纯数字部分
                        possible_codes.append(f"{suffix}{number_part}")  # 带前缀格式 (SH600001)

                    # 构建查询条件
                    code_conditions = " OR ".join([f"代码 = '{code}'" for code in possible_codes])
                    sql += f" WHERE ({code_conditions})"
                    self.update_log(f"检测到股票代码输入: {condition}")
                    self.update_log(f"尝试查询的代码格式: {', '.join(possible_codes)}")
                elif '=' not in condition and '>' not in condition and '<' not in condition and 'LIKE' not in condition.upper():
                    # 如果输入的不是标准SQL条件，尝试多种匹配方式
                    # 1. 尝试匹配代码
                    # 2. 尝试匹配名称（使用更灵活的模糊匹配）
                    sql += f" WHERE 代码 LIKE '%{condition}%' OR 名称 LIKE '%{condition}%'"
                    self.update_log(f"使用模糊匹配股票代码或名称: {condition}")
                else:
                    # 用户输入的是完整的SQL条件
                    sql += f" WHERE {condition}"

            self.update_log(f"执行SQL: {sql}")

            # 执行查询
            cursor.execute(sql)
            count = cursor.fetchone()[0]

            conn.close()

            self.update_log(f"表 {table_name} 共有 {count} 条记录" + (f" 满足条件: {condition}" if condition else ""))

            # 创建一个简单的DataFrame显示结果
            df = pd.DataFrame([{'表名': table_name, '记录数': count, '查询条件': condition or '无'}])
            self.signals.update_result.emit(df)

        except Exception as e:
            self.show_error(f"统计记录数时出错: {str(e)}")

    def execute_sql(self):
        """执行自定义SQL语句"""
        db_path = self.db_path_edit.text().strip()
        sql = self.sql_edit.toPlainText().strip()

        if not db_path:
            self.show_error("请指定数据库文件路径")
            return

        if not sql:
            self.show_error("请输入SQL语句")
            return

        if not os.path.exists(db_path):
            self.show_error(f"数据库文件不存在: {db_path}")
            return

        try:
            self.update_log(f"正在执行SQL语句...")

            conn = sqlite3.connect(db_path)

            # 判断是否是查询语句
            is_query = sql.strip().upper().startswith(('SELECT', 'PRAGMA', 'EXPLAIN'))

            if is_query:
                # 执行查询
                self.update_log(f"执行查询SQL: {sql}")
                df = pd.read_sql_query(sql, conn)
                self.signals.update_result.emit(df)
            else:
                # 执行非查询语句
                self.update_log(f"执行非查询SQL: {sql}")
                cursor = conn.cursor()
                cursor.execute(sql)
                conn.commit()
                self.update_log(f"SQL执行成功，影响 {cursor.rowcount} 行")

                # 显示一个简单的结果
                df = pd.DataFrame([{'操作': '非查询', '影响行数': cursor.rowcount, 'SQL': sql}])
                self.signals.update_result.emit(df)

            conn.close()

        except Exception as e:
            self.show_error(f"执行SQL时出错: {str(e)}")
